package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMODocumentGroup represents document groups
type PMODocumentGroup struct {
	BaseModel
	ProjectID     string    `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey        PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	GroupName     string    `json:"group_name" gorm:"column:group_name"`
	SharepointURL string    `json:"sharepoint_url" gorm:"column:sharepoint_url"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project       *PMOProject       `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItems []PMODocumentItem `json:"document_items,omitempty" gorm:"foreignKey:GroupID;references:ID"`
}

func (PMODocumentGroup) TableName() string {
	return "pmo_document_groups"
}

// PMODocumentItem represents document items
type PMODocumentItem struct {
	BaseModel
	ProjectID     string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey        PMOTabKey  `json:"tab_key" gorm:"column:tab_key"`
	GroupID       string     `json:"group_id" gorm:"column:group_id;type:uuid"`
	Name          string     `json:"name" gorm:"column:name"`
	SharepointURL string     `json:"sharepoint_url" gorm:"column:sharepoint_url"`
	Date          *time.Time `json:"date" gorm:"column:date;type:date"`
	Type          PMODocType `json:"type" gorm:"column:type"`
	FileID        *string    `json:"file_id" gorm:"column:file_id;type:uuid"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject       `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Group   *PMODocumentGroup `json:"group,omitempty" gorm:"foreignKey:GroupID;references:ID"`
	File    *File             `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
}

func (PMODocumentItem) TableName() string {
	return "pmo_document_items"
}

// PMODocumentItemVersion represents versioned document items
type PMODocumentItemVersion struct {
	PMODocumentItem
	OriginalID string `json:"document_item_id" gorm:"column:document_item_id;type:uuid;index"`
}

func (PMODocumentItemVersion) TableName() string {
	return "pmo_document_item_versions"
}

func (u *PMODocumentItemVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
