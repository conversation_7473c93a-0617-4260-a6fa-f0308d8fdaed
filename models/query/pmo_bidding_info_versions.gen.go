// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBiddingInfoVersion(db *gorm.DB, opts ...gen.DOOption) pMOBiddingInfoVersion {
	_pMOBiddingInfoVersion := pMOBiddingInfoVersion{}

	_pMOBiddingInfoVersion.pMOBiddingInfoVersionDo.UseDB(db, opts...)
	_pMOBiddingInfoVersion.pMOBiddingInfoVersionDo.UseModel(&models.PMOBiddingInfoVersion{})

	tableName := _pMOBiddingInfoVersion.pMOBiddingInfoVersionDo.TableName()
	_pMOBiddingInfoVersion.ALL = field.NewAsterisk(tableName)
	_pMOBiddingInfoVersion.ID = field.NewString(tableName, "id")
	_pMOBiddingInfoVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBiddingInfoVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBiddingInfoVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBiddingInfoVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOBiddingInfoVersion.BiddingType = field.NewString(tableName, "bidding_type")
	_pMOBiddingInfoVersion.BiddingValue = field.NewFloat64(tableName, "bidding_value")
	_pMOBiddingInfoVersion.TenderDate = field.NewTime(tableName, "tender_date")
	_pMOBiddingInfoVersion.TenderEntity = field.NewString(tableName, "tender_entity")
	_pMOBiddingInfoVersion.AnnounceDate = field.NewTime(tableName, "announce_date")
	_pMOBiddingInfoVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBiddingInfoVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBiddingInfoVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBiddingInfoVersion.OriginalID = field.NewString(tableName, "bidding_info_id")
	_pMOBiddingInfoVersion.Project = pMOBiddingInfoVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBiddingInfoVersion.CreatedBy = pMOBiddingInfoVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBiddingInfoVersion.UpdatedBy = pMOBiddingInfoVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBiddingInfoVersion.fillFieldMap()

	return _pMOBiddingInfoVersion
}

type pMOBiddingInfoVersion struct {
	pMOBiddingInfoVersionDo

	ALL          field.Asterisk
	ID           field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ProjectID    field.String
	BiddingType  field.String
	BiddingValue field.Float64
	TenderDate   field.Time
	TenderEntity field.String
	AnnounceDate field.Time
	CreatedByID  field.String
	UpdatedByID  field.String
	DeletedByID  field.String
	OriginalID   field.String
	Project      pMOBiddingInfoVersionHasOneProject

	CreatedBy pMOBiddingInfoVersionBelongsToCreatedBy

	UpdatedBy pMOBiddingInfoVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBiddingInfoVersion) Table(newTableName string) *pMOBiddingInfoVersion {
	p.pMOBiddingInfoVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBiddingInfoVersion) As(alias string) *pMOBiddingInfoVersion {
	p.pMOBiddingInfoVersionDo.DO = *(p.pMOBiddingInfoVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBiddingInfoVersion) updateTableName(table string) *pMOBiddingInfoVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.BiddingType = field.NewString(table, "bidding_type")
	p.BiddingValue = field.NewFloat64(table, "bidding_value")
	p.TenderDate = field.NewTime(table, "tender_date")
	p.TenderEntity = field.NewString(table, "tender_entity")
	p.AnnounceDate = field.NewTime(table, "announce_date")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "bidding_info_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBiddingInfoVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBiddingInfoVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 17)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["bidding_type"] = p.BiddingType
	p.fieldMap["bidding_value"] = p.BiddingValue
	p.fieldMap["tender_date"] = p.TenderDate
	p.fieldMap["tender_entity"] = p.TenderEntity
	p.fieldMap["announce_date"] = p.AnnounceDate
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["bidding_info_id"] = p.OriginalID

}

func (p pMOBiddingInfoVersion) clone(db *gorm.DB) pMOBiddingInfoVersion {
	p.pMOBiddingInfoVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBiddingInfoVersion) replaceDB(db *gorm.DB) pMOBiddingInfoVersion {
	p.pMOBiddingInfoVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBiddingInfoVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBiddingInfoVersionHasOneProject) Where(conds ...field.Expr) *pMOBiddingInfoVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoVersionHasOneProject) WithContext(ctx context.Context) *pMOBiddingInfoVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoVersionHasOneProject) Session(session *gorm.Session) *pMOBiddingInfoVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoVersionHasOneProject) Model(m *models.PMOBiddingInfoVersion) *pMOBiddingInfoVersionHasOneProjectTx {
	return &pMOBiddingInfoVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoVersionHasOneProject) Unscoped() *pMOBiddingInfoVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoVersionHasOneProjectTx) Unscoped() *pMOBiddingInfoVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBiddingInfoVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBiddingInfoVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBiddingInfoVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMOBiddingInfoVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoVersionBelongsToCreatedBy) Model(m *models.PMOBiddingInfoVersion) *pMOBiddingInfoVersionBelongsToCreatedByTx {
	return &pMOBiddingInfoVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoVersionBelongsToCreatedBy) Unscoped() *pMOBiddingInfoVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoVersionBelongsToCreatedByTx) Unscoped() *pMOBiddingInfoVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBiddingInfoVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBiddingInfoVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBiddingInfoVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBiddingInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoVersionBelongsToUpdatedBy) Model(m *models.PMOBiddingInfoVersion) *pMOBiddingInfoVersionBelongsToUpdatedByTx {
	return &pMOBiddingInfoVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoVersionBelongsToUpdatedBy) Unscoped() *pMOBiddingInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoVersionBelongsToUpdatedByTx) Unscoped() *pMOBiddingInfoVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoVersionDo struct{ gen.DO }

type IPMOBiddingInfoVersionDo interface {
	gen.SubQuery
	Debug() IPMOBiddingInfoVersionDo
	WithContext(ctx context.Context) IPMOBiddingInfoVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBiddingInfoVersionDo
	WriteDB() IPMOBiddingInfoVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBiddingInfoVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBiddingInfoVersionDo
	Not(conds ...gen.Condition) IPMOBiddingInfoVersionDo
	Or(conds ...gen.Condition) IPMOBiddingInfoVersionDo
	Select(conds ...field.Expr) IPMOBiddingInfoVersionDo
	Where(conds ...gen.Condition) IPMOBiddingInfoVersionDo
	Order(conds ...field.Expr) IPMOBiddingInfoVersionDo
	Distinct(cols ...field.Expr) IPMOBiddingInfoVersionDo
	Omit(cols ...field.Expr) IPMOBiddingInfoVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo
	Group(cols ...field.Expr) IPMOBiddingInfoVersionDo
	Having(conds ...gen.Condition) IPMOBiddingInfoVersionDo
	Limit(limit int) IPMOBiddingInfoVersionDo
	Offset(offset int) IPMOBiddingInfoVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBiddingInfoVersionDo
	Unscoped() IPMOBiddingInfoVersionDo
	Create(values ...*models.PMOBiddingInfoVersion) error
	CreateInBatches(values []*models.PMOBiddingInfoVersion, batchSize int) error
	Save(values ...*models.PMOBiddingInfoVersion) error
	First() (*models.PMOBiddingInfoVersion, error)
	Take() (*models.PMOBiddingInfoVersion, error)
	Last() (*models.PMOBiddingInfoVersion, error)
	Find() ([]*models.PMOBiddingInfoVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBiddingInfoVersion, err error)
	FindInBatches(result *[]*models.PMOBiddingInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBiddingInfoVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBiddingInfoVersionDo
	Assign(attrs ...field.AssignExpr) IPMOBiddingInfoVersionDo
	Joins(fields ...field.RelationField) IPMOBiddingInfoVersionDo
	Preload(fields ...field.RelationField) IPMOBiddingInfoVersionDo
	FirstOrInit() (*models.PMOBiddingInfoVersion, error)
	FirstOrCreate() (*models.PMOBiddingInfoVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOBiddingInfoVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBiddingInfoVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBiddingInfoVersionDo) Debug() IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBiddingInfoVersionDo) WithContext(ctx context.Context) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBiddingInfoVersionDo) ReadDB() IPMOBiddingInfoVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBiddingInfoVersionDo) WriteDB() IPMOBiddingInfoVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBiddingInfoVersionDo) Session(config *gorm.Session) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBiddingInfoVersionDo) Clauses(conds ...clause.Expression) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBiddingInfoVersionDo) Returning(value interface{}, columns ...string) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBiddingInfoVersionDo) Not(conds ...gen.Condition) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBiddingInfoVersionDo) Or(conds ...gen.Condition) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBiddingInfoVersionDo) Select(conds ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBiddingInfoVersionDo) Where(conds ...gen.Condition) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBiddingInfoVersionDo) Order(conds ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBiddingInfoVersionDo) Distinct(cols ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBiddingInfoVersionDo) Omit(cols ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBiddingInfoVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBiddingInfoVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBiddingInfoVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBiddingInfoVersionDo) Group(cols ...field.Expr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBiddingInfoVersionDo) Having(conds ...gen.Condition) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBiddingInfoVersionDo) Limit(limit int) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBiddingInfoVersionDo) Offset(offset int) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBiddingInfoVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBiddingInfoVersionDo) Unscoped() IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBiddingInfoVersionDo) Create(values ...*models.PMOBiddingInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBiddingInfoVersionDo) CreateInBatches(values []*models.PMOBiddingInfoVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBiddingInfoVersionDo) Save(values ...*models.PMOBiddingInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBiddingInfoVersionDo) First() (*models.PMOBiddingInfoVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfoVersion), nil
	}
}

func (p pMOBiddingInfoVersionDo) Take() (*models.PMOBiddingInfoVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfoVersion), nil
	}
}

func (p pMOBiddingInfoVersionDo) Last() (*models.PMOBiddingInfoVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfoVersion), nil
	}
}

func (p pMOBiddingInfoVersionDo) Find() ([]*models.PMOBiddingInfoVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBiddingInfoVersion), err
}

func (p pMOBiddingInfoVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBiddingInfoVersion, err error) {
	buf := make([]*models.PMOBiddingInfoVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBiddingInfoVersionDo) FindInBatches(result *[]*models.PMOBiddingInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBiddingInfoVersionDo) Attrs(attrs ...field.AssignExpr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBiddingInfoVersionDo) Assign(attrs ...field.AssignExpr) IPMOBiddingInfoVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBiddingInfoVersionDo) Joins(fields ...field.RelationField) IPMOBiddingInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBiddingInfoVersionDo) Preload(fields ...field.RelationField) IPMOBiddingInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBiddingInfoVersionDo) FirstOrInit() (*models.PMOBiddingInfoVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfoVersion), nil
	}
}

func (p pMOBiddingInfoVersionDo) FirstOrCreate() (*models.PMOBiddingInfoVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfoVersion), nil
	}
}

func (p pMOBiddingInfoVersionDo) FindByPage(offset int, limit int) (result []*models.PMOBiddingInfoVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBiddingInfoVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBiddingInfoVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBiddingInfoVersionDo) Delete(models ...*models.PMOBiddingInfoVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBiddingInfoVersionDo) withDO(do gen.Dao) *pMOBiddingInfoVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
