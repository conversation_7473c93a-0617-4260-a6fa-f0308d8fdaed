// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOChecklistItem(db *gorm.DB, opts ...gen.DOOption) pMOChecklistItem {
	_pMOChecklistItem := pMOChecklistItem{}

	_pMOChecklistItem.pMOChecklistItemDo.UseDB(db, opts...)
	_pMOChecklistItem.pMOChecklistItemDo.UseModel(&models.PMOChecklistItem{})

	tableName := _pMOChecklistItem.pMOChecklistItemDo.TableName()
	_pMOChecklistItem.ALL = field.NewAsterisk(tableName)
	_pMOChecklistItem.ID = field.NewString(tableName, "id")
	_pMOChecklistItem.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOChecklistItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOChecklistItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOChecklistItem.ProjectID = field.NewString(tableName, "project_id")
	_pMOChecklistItem.TabKey = field.NewString(tableName, "tab_key")
	_pMOChecklistItem.Detail = field.NewString(tableName, "detail")
	_pMOChecklistItem.IsChecked = field.NewBool(tableName, "is_checked")
	_pMOChecklistItem.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOChecklistItem.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOChecklistItem.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOChecklistItem.Project = pMOChecklistItemHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOChecklistItem.CreatedBy = pMOChecklistItemBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOChecklistItem.UpdatedBy = pMOChecklistItemBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOChecklistItem.fillFieldMap()

	return _pMOChecklistItem
}

type pMOChecklistItem struct {
	pMOChecklistItemDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	ProjectID   field.String
	TabKey      field.String
	Detail      field.String
	IsChecked   field.Bool
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	Project     pMOChecklistItemHasOneProject

	CreatedBy pMOChecklistItemBelongsToCreatedBy

	UpdatedBy pMOChecklistItemBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOChecklistItem) Table(newTableName string) *pMOChecklistItem {
	p.pMOChecklistItemDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOChecklistItem) As(alias string) *pMOChecklistItem {
	p.pMOChecklistItemDo.DO = *(p.pMOChecklistItemDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOChecklistItem) updateTableName(table string) *pMOChecklistItem {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.TabKey = field.NewString(table, "tab_key")
	p.Detail = field.NewString(table, "detail")
	p.IsChecked = field.NewBool(table, "is_checked")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOChecklistItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOChecklistItem) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 14)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["detail"] = p.Detail
	p.fieldMap["is_checked"] = p.IsChecked
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOChecklistItem) clone(db *gorm.DB) pMOChecklistItem {
	p.pMOChecklistItemDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOChecklistItem) replaceDB(db *gorm.DB) pMOChecklistItem {
	p.pMOChecklistItemDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOChecklistItemHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOChecklistItemHasOneProject) Where(conds ...field.Expr) *pMOChecklistItemHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOChecklistItemHasOneProject) WithContext(ctx context.Context) *pMOChecklistItemHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOChecklistItemHasOneProject) Session(session *gorm.Session) *pMOChecklistItemHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOChecklistItemHasOneProject) Model(m *models.PMOChecklistItem) *pMOChecklistItemHasOneProjectTx {
	return &pMOChecklistItemHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOChecklistItemHasOneProject) Unscoped() *pMOChecklistItemHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOChecklistItemHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOChecklistItemHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOChecklistItemHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOChecklistItemHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOChecklistItemHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOChecklistItemHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOChecklistItemHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOChecklistItemHasOneProjectTx) Unscoped() *pMOChecklistItemHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOChecklistItemBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOChecklistItemBelongsToCreatedBy) Where(conds ...field.Expr) *pMOChecklistItemBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOChecklistItemBelongsToCreatedBy) WithContext(ctx context.Context) *pMOChecklistItemBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOChecklistItemBelongsToCreatedBy) Session(session *gorm.Session) *pMOChecklistItemBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOChecklistItemBelongsToCreatedBy) Model(m *models.PMOChecklistItem) *pMOChecklistItemBelongsToCreatedByTx {
	return &pMOChecklistItemBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOChecklistItemBelongsToCreatedBy) Unscoped() *pMOChecklistItemBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOChecklistItemBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOChecklistItemBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOChecklistItemBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOChecklistItemBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOChecklistItemBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOChecklistItemBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOChecklistItemBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOChecklistItemBelongsToCreatedByTx) Unscoped() *pMOChecklistItemBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOChecklistItemBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOChecklistItemBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOChecklistItemBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOChecklistItemBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOChecklistItemBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOChecklistItemBelongsToUpdatedBy) Session(session *gorm.Session) *pMOChecklistItemBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOChecklistItemBelongsToUpdatedBy) Model(m *models.PMOChecklistItem) *pMOChecklistItemBelongsToUpdatedByTx {
	return &pMOChecklistItemBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOChecklistItemBelongsToUpdatedBy) Unscoped() *pMOChecklistItemBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOChecklistItemBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOChecklistItemBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOChecklistItemBelongsToUpdatedByTx) Unscoped() *pMOChecklistItemBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOChecklistItemDo struct{ gen.DO }

type IPMOChecklistItemDo interface {
	gen.SubQuery
	Debug() IPMOChecklistItemDo
	WithContext(ctx context.Context) IPMOChecklistItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOChecklistItemDo
	WriteDB() IPMOChecklistItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOChecklistItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOChecklistItemDo
	Not(conds ...gen.Condition) IPMOChecklistItemDo
	Or(conds ...gen.Condition) IPMOChecklistItemDo
	Select(conds ...field.Expr) IPMOChecklistItemDo
	Where(conds ...gen.Condition) IPMOChecklistItemDo
	Order(conds ...field.Expr) IPMOChecklistItemDo
	Distinct(cols ...field.Expr) IPMOChecklistItemDo
	Omit(cols ...field.Expr) IPMOChecklistItemDo
	Join(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo
	Group(cols ...field.Expr) IPMOChecklistItemDo
	Having(conds ...gen.Condition) IPMOChecklistItemDo
	Limit(limit int) IPMOChecklistItemDo
	Offset(offset int) IPMOChecklistItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOChecklistItemDo
	Unscoped() IPMOChecklistItemDo
	Create(values ...*models.PMOChecklistItem) error
	CreateInBatches(values []*models.PMOChecklistItem, batchSize int) error
	Save(values ...*models.PMOChecklistItem) error
	First() (*models.PMOChecklistItem, error)
	Take() (*models.PMOChecklistItem, error)
	Last() (*models.PMOChecklistItem, error)
	Find() ([]*models.PMOChecklistItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOChecklistItem, err error)
	FindInBatches(result *[]*models.PMOChecklistItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOChecklistItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOChecklistItemDo
	Assign(attrs ...field.AssignExpr) IPMOChecklistItemDo
	Joins(fields ...field.RelationField) IPMOChecklistItemDo
	Preload(fields ...field.RelationField) IPMOChecklistItemDo
	FirstOrInit() (*models.PMOChecklistItem, error)
	FirstOrCreate() (*models.PMOChecklistItem, error)
	FindByPage(offset int, limit int) (result []*models.PMOChecklistItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOChecklistItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOChecklistItemDo) Debug() IPMOChecklistItemDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOChecklistItemDo) WithContext(ctx context.Context) IPMOChecklistItemDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOChecklistItemDo) ReadDB() IPMOChecklistItemDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOChecklistItemDo) WriteDB() IPMOChecklistItemDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOChecklistItemDo) Session(config *gorm.Session) IPMOChecklistItemDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOChecklistItemDo) Clauses(conds ...clause.Expression) IPMOChecklistItemDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOChecklistItemDo) Returning(value interface{}, columns ...string) IPMOChecklistItemDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOChecklistItemDo) Not(conds ...gen.Condition) IPMOChecklistItemDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOChecklistItemDo) Or(conds ...gen.Condition) IPMOChecklistItemDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOChecklistItemDo) Select(conds ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOChecklistItemDo) Where(conds ...gen.Condition) IPMOChecklistItemDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOChecklistItemDo) Order(conds ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOChecklistItemDo) Distinct(cols ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOChecklistItemDo) Omit(cols ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOChecklistItemDo) Join(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOChecklistItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOChecklistItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOChecklistItemDo) Group(cols ...field.Expr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOChecklistItemDo) Having(conds ...gen.Condition) IPMOChecklistItemDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOChecklistItemDo) Limit(limit int) IPMOChecklistItemDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOChecklistItemDo) Offset(offset int) IPMOChecklistItemDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOChecklistItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOChecklistItemDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOChecklistItemDo) Unscoped() IPMOChecklistItemDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOChecklistItemDo) Create(values ...*models.PMOChecklistItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOChecklistItemDo) CreateInBatches(values []*models.PMOChecklistItem, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOChecklistItemDo) Save(values ...*models.PMOChecklistItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOChecklistItemDo) First() (*models.PMOChecklistItem, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOChecklistItem), nil
	}
}

func (p pMOChecklistItemDo) Take() (*models.PMOChecklistItem, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOChecklistItem), nil
	}
}

func (p pMOChecklistItemDo) Last() (*models.PMOChecklistItem, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOChecklistItem), nil
	}
}

func (p pMOChecklistItemDo) Find() ([]*models.PMOChecklistItem, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOChecklistItem), err
}

func (p pMOChecklistItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOChecklistItem, err error) {
	buf := make([]*models.PMOChecklistItem, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOChecklistItemDo) FindInBatches(result *[]*models.PMOChecklistItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOChecklistItemDo) Attrs(attrs ...field.AssignExpr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOChecklistItemDo) Assign(attrs ...field.AssignExpr) IPMOChecklistItemDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOChecklistItemDo) Joins(fields ...field.RelationField) IPMOChecklistItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOChecklistItemDo) Preload(fields ...field.RelationField) IPMOChecklistItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOChecklistItemDo) FirstOrInit() (*models.PMOChecklistItem, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOChecklistItem), nil
	}
}

func (p pMOChecklistItemDo) FirstOrCreate() (*models.PMOChecklistItem, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOChecklistItem), nil
	}
}

func (p pMOChecklistItemDo) FindByPage(offset int, limit int) (result []*models.PMOChecklistItem, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOChecklistItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOChecklistItemDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOChecklistItemDo) Delete(models ...*models.PMOChecklistItem) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOChecklistItemDo) withDO(do gen.Dao) *pMOChecklistItemDo {
	p.DO = *do.(*gen.DO)
	return p
}
