// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOCommentVersion(db *gorm.DB, opts ...gen.DOOption) pMOCommentVersion {
	_pMOCommentVersion := pMOCommentVersion{}

	_pMOCommentVersion.pMOCommentVersionDo.UseDB(db, opts...)
	_pMOCommentVersion.pMOCommentVersionDo.UseModel(&models.PMOCommentVersion{})

	tableName := _pMOCommentVersion.pMOCommentVersionDo.TableName()
	_pMOCommentVersion.ALL = field.NewAsterisk(tableName)
	_pMOCommentVersion.ID = field.NewString(tableName, "id")
	_pMOCommentVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOCommentVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOCommentVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOCommentVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOCommentVersion.UserID = field.NewString(tableName, "user_id")
	_pMOCommentVersion.Channel = field.NewString(tableName, "channel")
	_pMOCommentVersion.Detail = field.NewString(tableName, "detail")
	_pMOCommentVersion.IsClientFlag = field.NewBool(tableName, "is_client_flag")
	_pMOCommentVersion.ParentCommentID = field.NewString(tableName, "parent_comment_id")
	_pMOCommentVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOCommentVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOCommentVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOCommentVersion.OriginalID = field.NewString(tableName, "comment_id")
	_pMOCommentVersion.Project = pMOCommentVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOCommentVersion.Replies = pMOCommentVersionHasManyReplies{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Replies", "models.PMOComment"),
	}

	_pMOCommentVersion.User = pMOCommentVersionBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
	}

	_pMOCommentVersion.fillFieldMap()

	return _pMOCommentVersion
}

type pMOCommentVersion struct {
	pMOCommentVersionDo

	ALL             field.Asterisk
	ID              field.String
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	ProjectID       field.String
	UserID          field.String
	Channel         field.String
	Detail          field.String
	IsClientFlag    field.Bool
	ParentCommentID field.String
	CreatedByID     field.String
	UpdatedByID     field.String
	DeletedByID     field.String
	OriginalID      field.String
	Project         pMOCommentVersionHasOneProject

	Replies pMOCommentVersionHasManyReplies

	User pMOCommentVersionBelongsToUser

	fieldMap map[string]field.Expr
}

func (p pMOCommentVersion) Table(newTableName string) *pMOCommentVersion {
	p.pMOCommentVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOCommentVersion) As(alias string) *pMOCommentVersion {
	p.pMOCommentVersionDo.DO = *(p.pMOCommentVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOCommentVersion) updateTableName(table string) *pMOCommentVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.UserID = field.NewString(table, "user_id")
	p.Channel = field.NewString(table, "channel")
	p.Detail = field.NewString(table, "detail")
	p.IsClientFlag = field.NewBool(table, "is_client_flag")
	p.ParentCommentID = field.NewString(table, "parent_comment_id")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "comment_id")

	p.fillFieldMap()

	return p
}

func (p *pMOCommentVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOCommentVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 17)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["channel"] = p.Channel
	p.fieldMap["detail"] = p.Detail
	p.fieldMap["is_client_flag"] = p.IsClientFlag
	p.fieldMap["parent_comment_id"] = p.ParentCommentID
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["comment_id"] = p.OriginalID

}

func (p pMOCommentVersion) clone(db *gorm.DB) pMOCommentVersion {
	p.pMOCommentVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.Replies.db = db.Session(&gorm.Session{Initialized: true})
	p.Replies.db.Statement.ConnPool = db.Statement.ConnPool
	p.User.db = db.Session(&gorm.Session{Initialized: true})
	p.User.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOCommentVersion) replaceDB(db *gorm.DB) pMOCommentVersion {
	p.pMOCommentVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.Replies.db = db.Session(&gorm.Session{})
	p.User.db = db.Session(&gorm.Session{})
	return p
}

type pMOCommentVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOCommentVersionHasOneProject) Where(conds ...field.Expr) *pMOCommentVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentVersionHasOneProject) WithContext(ctx context.Context) *pMOCommentVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentVersionHasOneProject) Session(session *gorm.Session) *pMOCommentVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentVersionHasOneProject) Model(m *models.PMOCommentVersion) *pMOCommentVersionHasOneProjectTx {
	return &pMOCommentVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentVersionHasOneProject) Unscoped() *pMOCommentVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOCommentVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentVersionHasOneProjectTx) Unscoped() *pMOCommentVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentVersionHasManyReplies struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCommentVersionHasManyReplies) Where(conds ...field.Expr) *pMOCommentVersionHasManyReplies {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentVersionHasManyReplies) WithContext(ctx context.Context) *pMOCommentVersionHasManyReplies {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentVersionHasManyReplies) Session(session *gorm.Session) *pMOCommentVersionHasManyReplies {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentVersionHasManyReplies) Model(m *models.PMOCommentVersion) *pMOCommentVersionHasManyRepliesTx {
	return &pMOCommentVersionHasManyRepliesTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentVersionHasManyReplies) Unscoped() *pMOCommentVersionHasManyReplies {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentVersionHasManyRepliesTx struct{ tx *gorm.Association }

func (a pMOCommentVersionHasManyRepliesTx) Find() (result []*models.PMOComment, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentVersionHasManyRepliesTx) Append(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentVersionHasManyRepliesTx) Replace(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentVersionHasManyRepliesTx) Delete(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentVersionHasManyRepliesTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentVersionHasManyRepliesTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentVersionHasManyRepliesTx) Unscoped() *pMOCommentVersionHasManyRepliesTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentVersionBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCommentVersionBelongsToUser) Where(conds ...field.Expr) *pMOCommentVersionBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentVersionBelongsToUser) WithContext(ctx context.Context) *pMOCommentVersionBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentVersionBelongsToUser) Session(session *gorm.Session) *pMOCommentVersionBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentVersionBelongsToUser) Model(m *models.PMOCommentVersion) *pMOCommentVersionBelongsToUserTx {
	return &pMOCommentVersionBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentVersionBelongsToUser) Unscoped() *pMOCommentVersionBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentVersionBelongsToUserTx struct{ tx *gorm.Association }

func (a pMOCommentVersionBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentVersionBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentVersionBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentVersionBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentVersionBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentVersionBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentVersionBelongsToUserTx) Unscoped() *pMOCommentVersionBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentVersionDo struct{ gen.DO }

type IPMOCommentVersionDo interface {
	gen.SubQuery
	Debug() IPMOCommentVersionDo
	WithContext(ctx context.Context) IPMOCommentVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOCommentVersionDo
	WriteDB() IPMOCommentVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOCommentVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOCommentVersionDo
	Not(conds ...gen.Condition) IPMOCommentVersionDo
	Or(conds ...gen.Condition) IPMOCommentVersionDo
	Select(conds ...field.Expr) IPMOCommentVersionDo
	Where(conds ...gen.Condition) IPMOCommentVersionDo
	Order(conds ...field.Expr) IPMOCommentVersionDo
	Distinct(cols ...field.Expr) IPMOCommentVersionDo
	Omit(cols ...field.Expr) IPMOCommentVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo
	Group(cols ...field.Expr) IPMOCommentVersionDo
	Having(conds ...gen.Condition) IPMOCommentVersionDo
	Limit(limit int) IPMOCommentVersionDo
	Offset(offset int) IPMOCommentVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCommentVersionDo
	Unscoped() IPMOCommentVersionDo
	Create(values ...*models.PMOCommentVersion) error
	CreateInBatches(values []*models.PMOCommentVersion, batchSize int) error
	Save(values ...*models.PMOCommentVersion) error
	First() (*models.PMOCommentVersion, error)
	Take() (*models.PMOCommentVersion, error)
	Last() (*models.PMOCommentVersion, error)
	Find() ([]*models.PMOCommentVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOCommentVersion, err error)
	FindInBatches(result *[]*models.PMOCommentVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOCommentVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOCommentVersionDo
	Assign(attrs ...field.AssignExpr) IPMOCommentVersionDo
	Joins(fields ...field.RelationField) IPMOCommentVersionDo
	Preload(fields ...field.RelationField) IPMOCommentVersionDo
	FirstOrInit() (*models.PMOCommentVersion, error)
	FirstOrCreate() (*models.PMOCommentVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOCommentVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOCommentVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOCommentVersionDo) Debug() IPMOCommentVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOCommentVersionDo) WithContext(ctx context.Context) IPMOCommentVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOCommentVersionDo) ReadDB() IPMOCommentVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOCommentVersionDo) WriteDB() IPMOCommentVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOCommentVersionDo) Session(config *gorm.Session) IPMOCommentVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOCommentVersionDo) Clauses(conds ...clause.Expression) IPMOCommentVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOCommentVersionDo) Returning(value interface{}, columns ...string) IPMOCommentVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOCommentVersionDo) Not(conds ...gen.Condition) IPMOCommentVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOCommentVersionDo) Or(conds ...gen.Condition) IPMOCommentVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOCommentVersionDo) Select(conds ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOCommentVersionDo) Where(conds ...gen.Condition) IPMOCommentVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOCommentVersionDo) Order(conds ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOCommentVersionDo) Distinct(cols ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOCommentVersionDo) Omit(cols ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOCommentVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOCommentVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOCommentVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOCommentVersionDo) Group(cols ...field.Expr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOCommentVersionDo) Having(conds ...gen.Condition) IPMOCommentVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOCommentVersionDo) Limit(limit int) IPMOCommentVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOCommentVersionDo) Offset(offset int) IPMOCommentVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOCommentVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCommentVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOCommentVersionDo) Unscoped() IPMOCommentVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOCommentVersionDo) Create(values ...*models.PMOCommentVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOCommentVersionDo) CreateInBatches(values []*models.PMOCommentVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOCommentVersionDo) Save(values ...*models.PMOCommentVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOCommentVersionDo) First() (*models.PMOCommentVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCommentVersion), nil
	}
}

func (p pMOCommentVersionDo) Take() (*models.PMOCommentVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCommentVersion), nil
	}
}

func (p pMOCommentVersionDo) Last() (*models.PMOCommentVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCommentVersion), nil
	}
}

func (p pMOCommentVersionDo) Find() ([]*models.PMOCommentVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOCommentVersion), err
}

func (p pMOCommentVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOCommentVersion, err error) {
	buf := make([]*models.PMOCommentVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOCommentVersionDo) FindInBatches(result *[]*models.PMOCommentVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOCommentVersionDo) Attrs(attrs ...field.AssignExpr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOCommentVersionDo) Assign(attrs ...field.AssignExpr) IPMOCommentVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOCommentVersionDo) Joins(fields ...field.RelationField) IPMOCommentVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOCommentVersionDo) Preload(fields ...field.RelationField) IPMOCommentVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOCommentVersionDo) FirstOrInit() (*models.PMOCommentVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCommentVersion), nil
	}
}

func (p pMOCommentVersionDo) FirstOrCreate() (*models.PMOCommentVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCommentVersion), nil
	}
}

func (p pMOCommentVersionDo) FindByPage(offset int, limit int) (result []*models.PMOCommentVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOCommentVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOCommentVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOCommentVersionDo) Delete(models ...*models.PMOCommentVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOCommentVersionDo) withDO(do gen.Dao) *pMOCommentVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
