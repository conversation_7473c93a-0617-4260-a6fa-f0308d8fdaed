// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOComment(db *gorm.DB, opts ...gen.DOOption) pMOComment {
	_pMOComment := pMOComment{}

	_pMOComment.pMOCommentDo.UseDB(db, opts...)
	_pMOComment.pMOCommentDo.UseModel(&models.PMOComment{})

	tableName := _pMOComment.pMOCommentDo.TableName()
	_pMOComment.ALL = field.NewAsterisk(tableName)
	_pMOComment.ID = field.NewString(tableName, "id")
	_pMOComment.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOComment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOComment.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOComment.ProjectID = field.NewString(tableName, "project_id")
	_pMOComment.UserID = field.NewString(tableName, "user_id")
	_pMOComment.Channel = field.NewString(tableName, "channel")
	_pMOComment.Detail = field.NewString(tableName, "detail")
	_pMOComment.IsClientFlag = field.NewBool(tableName, "is_client_flag")
	_pMOComment.ParentCommentID = field.NewString(tableName, "parent_comment_id")
	_pMOComment.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOComment.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOComment.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOComment.Project = pMOCommentHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOComment.Replies = pMOCommentHasManyReplies{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Replies", "models.PMOComment"),
	}

	_pMOComment.User = pMOCommentBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
	}

	_pMOComment.fillFieldMap()

	return _pMOComment
}

type pMOComment struct {
	pMOCommentDo

	ALL             field.Asterisk
	ID              field.String
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	ProjectID       field.String
	UserID          field.String
	Channel         field.String
	Detail          field.String
	IsClientFlag    field.Bool
	ParentCommentID field.String
	CreatedByID     field.String
	UpdatedByID     field.String
	DeletedByID     field.String
	Project         pMOCommentHasOneProject

	Replies pMOCommentHasManyReplies

	User pMOCommentBelongsToUser

	fieldMap map[string]field.Expr
}

func (p pMOComment) Table(newTableName string) *pMOComment {
	p.pMOCommentDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOComment) As(alias string) *pMOComment {
	p.pMOCommentDo.DO = *(p.pMOCommentDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOComment) updateTableName(table string) *pMOComment {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.UserID = field.NewString(table, "user_id")
	p.Channel = field.NewString(table, "channel")
	p.Detail = field.NewString(table, "detail")
	p.IsClientFlag = field.NewBool(table, "is_client_flag")
	p.ParentCommentID = field.NewString(table, "parent_comment_id")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOComment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOComment) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["channel"] = p.Channel
	p.fieldMap["detail"] = p.Detail
	p.fieldMap["is_client_flag"] = p.IsClientFlag
	p.fieldMap["parent_comment_id"] = p.ParentCommentID
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOComment) clone(db *gorm.DB) pMOComment {
	p.pMOCommentDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.Replies.db = db.Session(&gorm.Session{Initialized: true})
	p.Replies.db.Statement.ConnPool = db.Statement.ConnPool
	p.User.db = db.Session(&gorm.Session{Initialized: true})
	p.User.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOComment) replaceDB(db *gorm.DB) pMOComment {
	p.pMOCommentDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.Replies.db = db.Session(&gorm.Session{})
	p.User.db = db.Session(&gorm.Session{})
	return p
}

type pMOCommentHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOCommentHasOneProject) Where(conds ...field.Expr) *pMOCommentHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentHasOneProject) WithContext(ctx context.Context) *pMOCommentHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentHasOneProject) Session(session *gorm.Session) *pMOCommentHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentHasOneProject) Model(m *models.PMOComment) *pMOCommentHasOneProjectTx {
	return &pMOCommentHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentHasOneProject) Unscoped() *pMOCommentHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOCommentHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentHasOneProjectTx) Unscoped() *pMOCommentHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentHasManyReplies struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCommentHasManyReplies) Where(conds ...field.Expr) *pMOCommentHasManyReplies {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentHasManyReplies) WithContext(ctx context.Context) *pMOCommentHasManyReplies {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentHasManyReplies) Session(session *gorm.Session) *pMOCommentHasManyReplies {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentHasManyReplies) Model(m *models.PMOComment) *pMOCommentHasManyRepliesTx {
	return &pMOCommentHasManyRepliesTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentHasManyReplies) Unscoped() *pMOCommentHasManyReplies {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentHasManyRepliesTx struct{ tx *gorm.Association }

func (a pMOCommentHasManyRepliesTx) Find() (result []*models.PMOComment, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentHasManyRepliesTx) Append(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentHasManyRepliesTx) Replace(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentHasManyRepliesTx) Delete(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentHasManyRepliesTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentHasManyRepliesTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentHasManyRepliesTx) Unscoped() *pMOCommentHasManyRepliesTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCommentBelongsToUser) Where(conds ...field.Expr) *pMOCommentBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCommentBelongsToUser) WithContext(ctx context.Context) *pMOCommentBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCommentBelongsToUser) Session(session *gorm.Session) *pMOCommentBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCommentBelongsToUser) Model(m *models.PMOComment) *pMOCommentBelongsToUserTx {
	return &pMOCommentBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCommentBelongsToUser) Unscoped() *pMOCommentBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCommentBelongsToUserTx struct{ tx *gorm.Association }

func (a pMOCommentBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCommentBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCommentBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCommentBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCommentBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCommentBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCommentBelongsToUserTx) Unscoped() *pMOCommentBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCommentDo struct{ gen.DO }

type IPMOCommentDo interface {
	gen.SubQuery
	Debug() IPMOCommentDo
	WithContext(ctx context.Context) IPMOCommentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOCommentDo
	WriteDB() IPMOCommentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOCommentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOCommentDo
	Not(conds ...gen.Condition) IPMOCommentDo
	Or(conds ...gen.Condition) IPMOCommentDo
	Select(conds ...field.Expr) IPMOCommentDo
	Where(conds ...gen.Condition) IPMOCommentDo
	Order(conds ...field.Expr) IPMOCommentDo
	Distinct(cols ...field.Expr) IPMOCommentDo
	Omit(cols ...field.Expr) IPMOCommentDo
	Join(table schema.Tabler, on ...field.Expr) IPMOCommentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCommentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOCommentDo
	Group(cols ...field.Expr) IPMOCommentDo
	Having(conds ...gen.Condition) IPMOCommentDo
	Limit(limit int) IPMOCommentDo
	Offset(offset int) IPMOCommentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCommentDo
	Unscoped() IPMOCommentDo
	Create(values ...*models.PMOComment) error
	CreateInBatches(values []*models.PMOComment, batchSize int) error
	Save(values ...*models.PMOComment) error
	First() (*models.PMOComment, error)
	Take() (*models.PMOComment, error)
	Last() (*models.PMOComment, error)
	Find() ([]*models.PMOComment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOComment, err error)
	FindInBatches(result *[]*models.PMOComment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOComment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOCommentDo
	Assign(attrs ...field.AssignExpr) IPMOCommentDo
	Joins(fields ...field.RelationField) IPMOCommentDo
	Preload(fields ...field.RelationField) IPMOCommentDo
	FirstOrInit() (*models.PMOComment, error)
	FirstOrCreate() (*models.PMOComment, error)
	FindByPage(offset int, limit int) (result []*models.PMOComment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOCommentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOCommentDo) Debug() IPMOCommentDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOCommentDo) WithContext(ctx context.Context) IPMOCommentDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOCommentDo) ReadDB() IPMOCommentDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOCommentDo) WriteDB() IPMOCommentDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOCommentDo) Session(config *gorm.Session) IPMOCommentDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOCommentDo) Clauses(conds ...clause.Expression) IPMOCommentDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOCommentDo) Returning(value interface{}, columns ...string) IPMOCommentDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOCommentDo) Not(conds ...gen.Condition) IPMOCommentDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOCommentDo) Or(conds ...gen.Condition) IPMOCommentDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOCommentDo) Select(conds ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOCommentDo) Where(conds ...gen.Condition) IPMOCommentDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOCommentDo) Order(conds ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOCommentDo) Distinct(cols ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOCommentDo) Omit(cols ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOCommentDo) Join(table schema.Tabler, on ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOCommentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOCommentDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOCommentDo) Group(cols ...field.Expr) IPMOCommentDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOCommentDo) Having(conds ...gen.Condition) IPMOCommentDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOCommentDo) Limit(limit int) IPMOCommentDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOCommentDo) Offset(offset int) IPMOCommentDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOCommentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCommentDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOCommentDo) Unscoped() IPMOCommentDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOCommentDo) Create(values ...*models.PMOComment) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOCommentDo) CreateInBatches(values []*models.PMOComment, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOCommentDo) Save(values ...*models.PMOComment) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOCommentDo) First() (*models.PMOComment, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOComment), nil
	}
}

func (p pMOCommentDo) Take() (*models.PMOComment, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOComment), nil
	}
}

func (p pMOCommentDo) Last() (*models.PMOComment, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOComment), nil
	}
}

func (p pMOCommentDo) Find() ([]*models.PMOComment, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOComment), err
}

func (p pMOCommentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOComment, err error) {
	buf := make([]*models.PMOComment, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOCommentDo) FindInBatches(result *[]*models.PMOComment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOCommentDo) Attrs(attrs ...field.AssignExpr) IPMOCommentDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOCommentDo) Assign(attrs ...field.AssignExpr) IPMOCommentDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOCommentDo) Joins(fields ...field.RelationField) IPMOCommentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOCommentDo) Preload(fields ...field.RelationField) IPMOCommentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOCommentDo) FirstOrInit() (*models.PMOComment, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOComment), nil
	}
}

func (p pMOCommentDo) FirstOrCreate() (*models.PMOComment, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOComment), nil
	}
}

func (p pMOCommentDo) FindByPage(offset int, limit int) (result []*models.PMOComment, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOCommentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOCommentDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOCommentDo) Delete(models ...*models.PMOComment) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOCommentDo) withDO(do gen.Dao) *pMOCommentDo {
	p.DO = *do.(*gen.DO)
	return p
}
