// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newUserAccessLevel(db *gorm.DB, opts ...gen.DOOption) userAccessLevel {
	_userAccessLevel := userAccessLevel{}

	_userAccessLevel.userAccessLevelDo.UseDB(db, opts...)
	_userAccessLevel.userAccessLevelDo.UseModel(&models.UserAccessLevel{})

	tableName := _userAccessLevel.userAccessLevelDo.TableName()
	_userAccessLevel.ALL = field.NewAsterisk(tableName)
	_userAccessLevel.UserID = field.NewString(tableName, "user_id")
	_userAccessLevel.Clockin = field.NewString(tableName, "clockin")
	_userAccessLevel.Timesheet = field.NewString(tableName, "timesheet")
	_userAccessLevel.Pmo = field.NewString(tableName, "pmo")
	_userAccessLevel.Setting = field.NewString(tableName, "setting")
	_userAccessLevel.CreatedAt = field.NewTime(tableName, "created_at")
	_userAccessLevel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userAccessLevel.User = userAccessLevelBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Checkins.User", "models.User"),
			},
		},
	}

	_userAccessLevel.fillFieldMap()

	return _userAccessLevel
}

type userAccessLevel struct {
	userAccessLevelDo

	ALL       field.Asterisk
	UserID    field.String
	Clockin   field.String
	Timesheet field.String
	Pmo       field.String
	Setting   field.String
	CreatedAt field.Time
	UpdatedAt field.Time
	User      userAccessLevelBelongsToUser

	fieldMap map[string]field.Expr
}

func (u userAccessLevel) Table(newTableName string) *userAccessLevel {
	u.userAccessLevelDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userAccessLevel) As(alias string) *userAccessLevel {
	u.userAccessLevelDo.DO = *(u.userAccessLevelDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userAccessLevel) updateTableName(table string) *userAccessLevel {
	u.ALL = field.NewAsterisk(table)
	u.UserID = field.NewString(table, "user_id")
	u.Clockin = field.NewString(table, "clockin")
	u.Timesheet = field.NewString(table, "timesheet")
	u.Pmo = field.NewString(table, "pmo")
	u.Setting = field.NewString(table, "setting")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userAccessLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userAccessLevel) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 8)
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["clockin"] = u.Clockin
	u.fieldMap["timesheet"] = u.Timesheet
	u.fieldMap["pmo"] = u.Pmo
	u.fieldMap["setting"] = u.Setting
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt

}

func (u userAccessLevel) clone(db *gorm.DB) userAccessLevel {
	u.userAccessLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	u.User.db = db.Session(&gorm.Session{Initialized: true})
	u.User.db.Statement.ConnPool = db.Statement.ConnPool
	return u
}

func (u userAccessLevel) replaceDB(db *gorm.DB) userAccessLevel {
	u.userAccessLevelDo.ReplaceDB(db)
	u.User.db = db.Session(&gorm.Session{})
	return u
}

type userAccessLevelBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a userAccessLevelBelongsToUser) Where(conds ...field.Expr) *userAccessLevelBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userAccessLevelBelongsToUser) WithContext(ctx context.Context) *userAccessLevelBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userAccessLevelBelongsToUser) Session(session *gorm.Session) *userAccessLevelBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a userAccessLevelBelongsToUser) Model(m *models.UserAccessLevel) *userAccessLevelBelongsToUserTx {
	return &userAccessLevelBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a userAccessLevelBelongsToUser) Unscoped() *userAccessLevelBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type userAccessLevelBelongsToUserTx struct{ tx *gorm.Association }

func (a userAccessLevelBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a userAccessLevelBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userAccessLevelBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userAccessLevelBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userAccessLevelBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a userAccessLevelBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a userAccessLevelBelongsToUserTx) Unscoped() *userAccessLevelBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userAccessLevelDo struct{ gen.DO }

type IUserAccessLevelDo interface {
	gen.SubQuery
	Debug() IUserAccessLevelDo
	WithContext(ctx context.Context) IUserAccessLevelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserAccessLevelDo
	WriteDB() IUserAccessLevelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserAccessLevelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserAccessLevelDo
	Not(conds ...gen.Condition) IUserAccessLevelDo
	Or(conds ...gen.Condition) IUserAccessLevelDo
	Select(conds ...field.Expr) IUserAccessLevelDo
	Where(conds ...gen.Condition) IUserAccessLevelDo
	Order(conds ...field.Expr) IUserAccessLevelDo
	Distinct(cols ...field.Expr) IUserAccessLevelDo
	Omit(cols ...field.Expr) IUserAccessLevelDo
	Join(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo
	Group(cols ...field.Expr) IUserAccessLevelDo
	Having(conds ...gen.Condition) IUserAccessLevelDo
	Limit(limit int) IUserAccessLevelDo
	Offset(offset int) IUserAccessLevelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserAccessLevelDo
	Unscoped() IUserAccessLevelDo
	Create(values ...*models.UserAccessLevel) error
	CreateInBatches(values []*models.UserAccessLevel, batchSize int) error
	Save(values ...*models.UserAccessLevel) error
	First() (*models.UserAccessLevel, error)
	Take() (*models.UserAccessLevel, error)
	Last() (*models.UserAccessLevel, error)
	Find() ([]*models.UserAccessLevel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.UserAccessLevel, err error)
	FindInBatches(result *[]*models.UserAccessLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.UserAccessLevel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserAccessLevelDo
	Assign(attrs ...field.AssignExpr) IUserAccessLevelDo
	Joins(fields ...field.RelationField) IUserAccessLevelDo
	Preload(fields ...field.RelationField) IUserAccessLevelDo
	FirstOrInit() (*models.UserAccessLevel, error)
	FirstOrCreate() (*models.UserAccessLevel, error)
	FindByPage(offset int, limit int) (result []*models.UserAccessLevel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserAccessLevelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userAccessLevelDo) Debug() IUserAccessLevelDo {
	return u.withDO(u.DO.Debug())
}

func (u userAccessLevelDo) WithContext(ctx context.Context) IUserAccessLevelDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userAccessLevelDo) ReadDB() IUserAccessLevelDo {
	return u.Clauses(dbresolver.Read)
}

func (u userAccessLevelDo) WriteDB() IUserAccessLevelDo {
	return u.Clauses(dbresolver.Write)
}

func (u userAccessLevelDo) Session(config *gorm.Session) IUserAccessLevelDo {
	return u.withDO(u.DO.Session(config))
}

func (u userAccessLevelDo) Clauses(conds ...clause.Expression) IUserAccessLevelDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userAccessLevelDo) Returning(value interface{}, columns ...string) IUserAccessLevelDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userAccessLevelDo) Not(conds ...gen.Condition) IUserAccessLevelDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userAccessLevelDo) Or(conds ...gen.Condition) IUserAccessLevelDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userAccessLevelDo) Select(conds ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userAccessLevelDo) Where(conds ...gen.Condition) IUserAccessLevelDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userAccessLevelDo) Order(conds ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userAccessLevelDo) Distinct(cols ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userAccessLevelDo) Omit(cols ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userAccessLevelDo) Join(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userAccessLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userAccessLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userAccessLevelDo) Group(cols ...field.Expr) IUserAccessLevelDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userAccessLevelDo) Having(conds ...gen.Condition) IUserAccessLevelDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userAccessLevelDo) Limit(limit int) IUserAccessLevelDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userAccessLevelDo) Offset(offset int) IUserAccessLevelDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userAccessLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserAccessLevelDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userAccessLevelDo) Unscoped() IUserAccessLevelDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userAccessLevelDo) Create(values ...*models.UserAccessLevel) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userAccessLevelDo) CreateInBatches(values []*models.UserAccessLevel, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userAccessLevelDo) Save(values ...*models.UserAccessLevel) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userAccessLevelDo) First() (*models.UserAccessLevel, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.UserAccessLevel), nil
	}
}

func (u userAccessLevelDo) Take() (*models.UserAccessLevel, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.UserAccessLevel), nil
	}
}

func (u userAccessLevelDo) Last() (*models.UserAccessLevel, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.UserAccessLevel), nil
	}
}

func (u userAccessLevelDo) Find() ([]*models.UserAccessLevel, error) {
	result, err := u.DO.Find()
	return result.([]*models.UserAccessLevel), err
}

func (u userAccessLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.UserAccessLevel, err error) {
	buf := make([]*models.UserAccessLevel, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userAccessLevelDo) FindInBatches(result *[]*models.UserAccessLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userAccessLevelDo) Attrs(attrs ...field.AssignExpr) IUserAccessLevelDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userAccessLevelDo) Assign(attrs ...field.AssignExpr) IUserAccessLevelDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userAccessLevelDo) Joins(fields ...field.RelationField) IUserAccessLevelDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userAccessLevelDo) Preload(fields ...field.RelationField) IUserAccessLevelDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userAccessLevelDo) FirstOrInit() (*models.UserAccessLevel, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.UserAccessLevel), nil
	}
}

func (u userAccessLevelDo) FirstOrCreate() (*models.UserAccessLevel, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.UserAccessLevel), nil
	}
}

func (u userAccessLevelDo) FindByPage(offset int, limit int) (result []*models.UserAccessLevel, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userAccessLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userAccessLevelDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userAccessLevelDo) Delete(models ...*models.UserAccessLevel) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userAccessLevelDo) withDO(do gen.Dao) *userAccessLevelDo {
	u.DO = *do.(*gen.DO)
	return u
}
