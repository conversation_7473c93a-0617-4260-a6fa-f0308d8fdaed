// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newHoliday(db *gorm.DB, opts ...gen.DOOption) holiday {
	_holiday := holiday{}

	_holiday.holidayDo.UseDB(db, opts...)
	_holiday.holidayDo.UseModel(&models.Holiday{})

	tableName := _holiday.holidayDo.TableName()
	_holiday.ALL = field.NewAsterisk(tableName)
	_holiday.ID = field.NewString(tableName, "id")
	_holiday.CreatedAt = field.NewTime(tableName, "created_at")
	_holiday.UpdatedAt = field.NewTime(tableName, "updated_at")
	_holiday.Name = field.NewString(tableName, "name")
	_holiday.Date = field.NewTime(tableName, "date")
	_holiday.CreatedByID = field.NewString(tableName, "created_by_id")
	_holiday.UpdatedByID = field.NewString(tableName, "updated_by_id")

	_holiday.fillFieldMap()

	return _holiday
}

type holiday struct {
	holidayDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Name        field.String
	Date        field.Time
	CreatedByID field.String
	UpdatedByID field.String

	fieldMap map[string]field.Expr
}

func (h holiday) Table(newTableName string) *holiday {
	h.holidayDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h holiday) As(alias string) *holiday {
	h.holidayDo.DO = *(h.holidayDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *holiday) updateTableName(table string) *holiday {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewString(table, "id")
	h.CreatedAt = field.NewTime(table, "created_at")
	h.UpdatedAt = field.NewTime(table, "updated_at")
	h.Name = field.NewString(table, "name")
	h.Date = field.NewTime(table, "date")
	h.CreatedByID = field.NewString(table, "created_by_id")
	h.UpdatedByID = field.NewString(table, "updated_by_id")

	h.fillFieldMap()

	return h
}

func (h *holiday) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *holiday) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 7)
	h.fieldMap["id"] = h.ID
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
	h.fieldMap["name"] = h.Name
	h.fieldMap["date"] = h.Date
	h.fieldMap["created_by_id"] = h.CreatedByID
	h.fieldMap["updated_by_id"] = h.UpdatedByID
}

func (h holiday) clone(db *gorm.DB) holiday {
	h.holidayDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h holiday) replaceDB(db *gorm.DB) holiday {
	h.holidayDo.ReplaceDB(db)
	return h
}

type holidayDo struct{ gen.DO }

type IHolidayDo interface {
	gen.SubQuery
	Debug() IHolidayDo
	WithContext(ctx context.Context) IHolidayDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IHolidayDo
	WriteDB() IHolidayDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IHolidayDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IHolidayDo
	Not(conds ...gen.Condition) IHolidayDo
	Or(conds ...gen.Condition) IHolidayDo
	Select(conds ...field.Expr) IHolidayDo
	Where(conds ...gen.Condition) IHolidayDo
	Order(conds ...field.Expr) IHolidayDo
	Distinct(cols ...field.Expr) IHolidayDo
	Omit(cols ...field.Expr) IHolidayDo
	Join(table schema.Tabler, on ...field.Expr) IHolidayDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IHolidayDo
	RightJoin(table schema.Tabler, on ...field.Expr) IHolidayDo
	Group(cols ...field.Expr) IHolidayDo
	Having(conds ...gen.Condition) IHolidayDo
	Limit(limit int) IHolidayDo
	Offset(offset int) IHolidayDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IHolidayDo
	Unscoped() IHolidayDo
	Create(values ...*models.Holiday) error
	CreateInBatches(values []*models.Holiday, batchSize int) error
	Save(values ...*models.Holiday) error
	First() (*models.Holiday, error)
	Take() (*models.Holiday, error)
	Last() (*models.Holiday, error)
	Find() ([]*models.Holiday, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Holiday, err error)
	FindInBatches(result *[]*models.Holiday, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Holiday) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IHolidayDo
	Assign(attrs ...field.AssignExpr) IHolidayDo
	Joins(fields ...field.RelationField) IHolidayDo
	Preload(fields ...field.RelationField) IHolidayDo
	FirstOrInit() (*models.Holiday, error)
	FirstOrCreate() (*models.Holiday, error)
	FindByPage(offset int, limit int) (result []*models.Holiday, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IHolidayDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h holidayDo) Debug() IHolidayDo {
	return h.withDO(h.DO.Debug())
}

func (h holidayDo) WithContext(ctx context.Context) IHolidayDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h holidayDo) ReadDB() IHolidayDo {
	return h.Clauses(dbresolver.Read)
}

func (h holidayDo) WriteDB() IHolidayDo {
	return h.Clauses(dbresolver.Write)
}

func (h holidayDo) Session(config *gorm.Session) IHolidayDo {
	return h.withDO(h.DO.Session(config))
}

func (h holidayDo) Clauses(conds ...clause.Expression) IHolidayDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h holidayDo) Returning(value interface{}, columns ...string) IHolidayDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h holidayDo) Not(conds ...gen.Condition) IHolidayDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h holidayDo) Or(conds ...gen.Condition) IHolidayDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h holidayDo) Select(conds ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h holidayDo) Where(conds ...gen.Condition) IHolidayDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h holidayDo) Order(conds ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h holidayDo) Distinct(cols ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h holidayDo) Omit(cols ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h holidayDo) Join(table schema.Tabler, on ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h holidayDo) LeftJoin(table schema.Tabler, on ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h holidayDo) RightJoin(table schema.Tabler, on ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h holidayDo) Group(cols ...field.Expr) IHolidayDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h holidayDo) Having(conds ...gen.Condition) IHolidayDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h holidayDo) Limit(limit int) IHolidayDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h holidayDo) Offset(offset int) IHolidayDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h holidayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IHolidayDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h holidayDo) Unscoped() IHolidayDo {
	return h.withDO(h.DO.Unscoped())
}

func (h holidayDo) Create(values ...*models.Holiday) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h holidayDo) CreateInBatches(values []*models.Holiday, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h holidayDo) Save(values ...*models.Holiday) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h holidayDo) First() (*models.Holiday, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Holiday), nil
	}
}

func (h holidayDo) Take() (*models.Holiday, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Holiday), nil
	}
}

func (h holidayDo) Last() (*models.Holiday, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Holiday), nil
	}
}

func (h holidayDo) Find() ([]*models.Holiday, error) {
	result, err := h.DO.Find()
	return result.([]*models.Holiday), err
}

func (h holidayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Holiday, err error) {
	buf := make([]*models.Holiday, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h holidayDo) FindInBatches(result *[]*models.Holiday, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h holidayDo) Attrs(attrs ...field.AssignExpr) IHolidayDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h holidayDo) Assign(attrs ...field.AssignExpr) IHolidayDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h holidayDo) Joins(fields ...field.RelationField) IHolidayDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h holidayDo) Preload(fields ...field.RelationField) IHolidayDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h holidayDo) FirstOrInit() (*models.Holiday, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Holiday), nil
	}
}

func (h holidayDo) FirstOrCreate() (*models.Holiday, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Holiday), nil
	}
}

func (h holidayDo) FindByPage(offset int, limit int) (result []*models.Holiday, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h holidayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h holidayDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h holidayDo) Delete(models ...*models.Holiday) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *holidayDo) withDO(do gen.Dao) *holidayDo {
	h.DO = *do.(*gen.DO)
	return h
}
