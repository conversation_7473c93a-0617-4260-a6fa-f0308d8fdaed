// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                        = new(Query)
	Checkin                  *checkin
	Department               *department
	Holiday                  *holiday
	Ministry                 *ministry
	PMOBidbondInfo           *pMOBidbondInfo
	PMOBidbondInfoVersion    *pMOBidbondInfoVersion
	PMOBiddingInfo           *pMOBiddingInfo
	PMOBiddingInfoVersion    *pMOBiddingInfoVersion
	PMOBudgetInfo            *pMOBudgetInfo
	PMOBudgetInfoVersion     *pMOBudgetInfoVersion
	PMOChecklistItem         *pMOChecklistItem
	PMOCollaborator          *pMOCollaborator
	PMOComment               *pMOComment
	PMOCommentVersion        *pMOCommentVersion
	PMOCompetitor            *pMOCompetitor
	PMOContact               *pMOContact
	PMOContractInfo          *pMOContractInfo
	PMOContractInfoVersion   *pMOContractInfoVersion
	PMODocumentGroup         *pMODocumentGroup
	PMODocumentItem          *pMODocumentItem
	PMODocumentItemVersion   *pMODocumentItemVersion
	PMOLGInfo                *pMOLGInfo
	PMOLGInfoVersion         *pMOLGInfoVersion
	PMOPartner               *pMOPartner
	PMOProject               *pMOProject
	PMORemark                *pMORemark
	PMORemarkVersion         *pMORemarkVersion
	PMOTemplateChecklistItem *pMOTemplateChecklistItem
	PMOTemplateDocument      *pMOTemplateDocument
	PMOVendorItem            *pMOVendorItem
	Project                  *project
	Sga                      *sga
	Team                     *team
	Timesheet                *timesheet
	User                     *user
	UserAccessLevel          *userAccessLevel
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Checkin = &Q.Checkin
	Department = &Q.Department
	Holiday = &Q.Holiday
	Ministry = &Q.Ministry
	PMOBidbondInfo = &Q.PMOBidbondInfo
	PMOBidbondInfoVersion = &Q.PMOBidbondInfoVersion
	PMOBiddingInfo = &Q.PMOBiddingInfo
	PMOBiddingInfoVersion = &Q.PMOBiddingInfoVersion
	PMOBudgetInfo = &Q.PMOBudgetInfo
	PMOBudgetInfoVersion = &Q.PMOBudgetInfoVersion
	PMOChecklistItem = &Q.PMOChecklistItem
	PMOCollaborator = &Q.PMOCollaborator
	PMOComment = &Q.PMOComment
	PMOCommentVersion = &Q.PMOCommentVersion
	PMOCompetitor = &Q.PMOCompetitor
	PMOContact = &Q.PMOContact
	PMOContractInfo = &Q.PMOContractInfo
	PMOContractInfoVersion = &Q.PMOContractInfoVersion
	PMODocumentGroup = &Q.PMODocumentGroup
	PMODocumentItem = &Q.PMODocumentItem
	PMODocumentItemVersion = &Q.PMODocumentItemVersion
	PMOLGInfo = &Q.PMOLGInfo
	PMOLGInfoVersion = &Q.PMOLGInfoVersion
	PMOPartner = &Q.PMOPartner
	PMOProject = &Q.PMOProject
	PMORemark = &Q.PMORemark
	PMORemarkVersion = &Q.PMORemarkVersion
	PMOTemplateChecklistItem = &Q.PMOTemplateChecklistItem
	PMOTemplateDocument = &Q.PMOTemplateDocument
	PMOVendorItem = &Q.PMOVendorItem
	Project = &Q.Project
	Sga = &Q.Sga
	Team = &Q.Team
	Timesheet = &Q.Timesheet
	User = &Q.User
	UserAccessLevel = &Q.UserAccessLevel
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                       db,
		Checkin:                  newCheckin(db, opts...),
		Department:               newDepartment(db, opts...),
		Holiday:                  newHoliday(db, opts...),
		Ministry:                 newMinistry(db, opts...),
		PMOBidbondInfo:           newPMOBidbondInfo(db, opts...),
		PMOBidbondInfoVersion:    newPMOBidbondInfoVersion(db, opts...),
		PMOBiddingInfo:           newPMOBiddingInfo(db, opts...),
		PMOBiddingInfoVersion:    newPMOBiddingInfoVersion(db, opts...),
		PMOBudgetInfo:            newPMOBudgetInfo(db, opts...),
		PMOBudgetInfoVersion:     newPMOBudgetInfoVersion(db, opts...),
		PMOChecklistItem:         newPMOChecklistItem(db, opts...),
		PMOCollaborator:          newPMOCollaborator(db, opts...),
		PMOComment:               newPMOComment(db, opts...),
		PMOCommentVersion:        newPMOCommentVersion(db, opts...),
		PMOCompetitor:            newPMOCompetitor(db, opts...),
		PMOContact:               newPMOContact(db, opts...),
		PMOContractInfo:          newPMOContractInfo(db, opts...),
		PMOContractInfoVersion:   newPMOContractInfoVersion(db, opts...),
		PMODocumentGroup:         newPMODocumentGroup(db, opts...),
		PMODocumentItem:          newPMODocumentItem(db, opts...),
		PMODocumentItemVersion:   newPMODocumentItemVersion(db, opts...),
		PMOLGInfo:                newPMOLGInfo(db, opts...),
		PMOLGInfoVersion:         newPMOLGInfoVersion(db, opts...),
		PMOPartner:               newPMOPartner(db, opts...),
		PMOProject:               newPMOProject(db, opts...),
		PMORemark:                newPMORemark(db, opts...),
		PMORemarkVersion:         newPMORemarkVersion(db, opts...),
		PMOTemplateChecklistItem: newPMOTemplateChecklistItem(db, opts...),
		PMOTemplateDocument:      newPMOTemplateDocument(db, opts...),
		PMOVendorItem:            newPMOVendorItem(db, opts...),
		Project:                  newProject(db, opts...),
		Sga:                      newSga(db, opts...),
		Team:                     newTeam(db, opts...),
		Timesheet:                newTimesheet(db, opts...),
		User:                     newUser(db, opts...),
		UserAccessLevel:          newUserAccessLevel(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Checkin                  checkin
	Department               department
	Holiday                  holiday
	Ministry                 ministry
	PMOBidbondInfo           pMOBidbondInfo
	PMOBidbondInfoVersion    pMOBidbondInfoVersion
	PMOBiddingInfo           pMOBiddingInfo
	PMOBiddingInfoVersion    pMOBiddingInfoVersion
	PMOBudgetInfo            pMOBudgetInfo
	PMOBudgetInfoVersion     pMOBudgetInfoVersion
	PMOChecklistItem         pMOChecklistItem
	PMOCollaborator          pMOCollaborator
	PMOComment               pMOComment
	PMOCommentVersion        pMOCommentVersion
	PMOCompetitor            pMOCompetitor
	PMOContact               pMOContact
	PMOContractInfo          pMOContractInfo
	PMOContractInfoVersion   pMOContractInfoVersion
	PMODocumentGroup         pMODocumentGroup
	PMODocumentItem          pMODocumentItem
	PMODocumentItemVersion   pMODocumentItemVersion
	PMOLGInfo                pMOLGInfo
	PMOLGInfoVersion         pMOLGInfoVersion
	PMOPartner               pMOPartner
	PMOProject               pMOProject
	PMORemark                pMORemark
	PMORemarkVersion         pMORemarkVersion
	PMOTemplateChecklistItem pMOTemplateChecklistItem
	PMOTemplateDocument      pMOTemplateDocument
	PMOVendorItem            pMOVendorItem
	Project                  project
	Sga                      sga
	Team                     team
	Timesheet                timesheet
	User                     user
	UserAccessLevel          userAccessLevel
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                       db,
		Checkin:                  q.Checkin.clone(db),
		Department:               q.Department.clone(db),
		Holiday:                  q.Holiday.clone(db),
		Ministry:                 q.Ministry.clone(db),
		PMOBidbondInfo:           q.PMOBidbondInfo.clone(db),
		PMOBidbondInfoVersion:    q.PMOBidbondInfoVersion.clone(db),
		PMOBiddingInfo:           q.PMOBiddingInfo.clone(db),
		PMOBiddingInfoVersion:    q.PMOBiddingInfoVersion.clone(db),
		PMOBudgetInfo:            q.PMOBudgetInfo.clone(db),
		PMOBudgetInfoVersion:     q.PMOBudgetInfoVersion.clone(db),
		PMOChecklistItem:         q.PMOChecklistItem.clone(db),
		PMOCollaborator:          q.PMOCollaborator.clone(db),
		PMOComment:               q.PMOComment.clone(db),
		PMOCommentVersion:        q.PMOCommentVersion.clone(db),
		PMOCompetitor:            q.PMOCompetitor.clone(db),
		PMOContact:               q.PMOContact.clone(db),
		PMOContractInfo:          q.PMOContractInfo.clone(db),
		PMOContractInfoVersion:   q.PMOContractInfoVersion.clone(db),
		PMODocumentGroup:         q.PMODocumentGroup.clone(db),
		PMODocumentItem:          q.PMODocumentItem.clone(db),
		PMODocumentItemVersion:   q.PMODocumentItemVersion.clone(db),
		PMOLGInfo:                q.PMOLGInfo.clone(db),
		PMOLGInfoVersion:         q.PMOLGInfoVersion.clone(db),
		PMOPartner:               q.PMOPartner.clone(db),
		PMOProject:               q.PMOProject.clone(db),
		PMORemark:                q.PMORemark.clone(db),
		PMORemarkVersion:         q.PMORemarkVersion.clone(db),
		PMOTemplateChecklistItem: q.PMOTemplateChecklistItem.clone(db),
		PMOTemplateDocument:      q.PMOTemplateDocument.clone(db),
		PMOVendorItem:            q.PMOVendorItem.clone(db),
		Project:                  q.Project.clone(db),
		Sga:                      q.Sga.clone(db),
		Team:                     q.Team.clone(db),
		Timesheet:                q.Timesheet.clone(db),
		User:                     q.User.clone(db),
		UserAccessLevel:          q.UserAccessLevel.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                       db,
		Checkin:                  q.Checkin.replaceDB(db),
		Department:               q.Department.replaceDB(db),
		Holiday:                  q.Holiday.replaceDB(db),
		Ministry:                 q.Ministry.replaceDB(db),
		PMOBidbondInfo:           q.PMOBidbondInfo.replaceDB(db),
		PMOBidbondInfoVersion:    q.PMOBidbondInfoVersion.replaceDB(db),
		PMOBiddingInfo:           q.PMOBiddingInfo.replaceDB(db),
		PMOBiddingInfoVersion:    q.PMOBiddingInfoVersion.replaceDB(db),
		PMOBudgetInfo:            q.PMOBudgetInfo.replaceDB(db),
		PMOBudgetInfoVersion:     q.PMOBudgetInfoVersion.replaceDB(db),
		PMOChecklistItem:         q.PMOChecklistItem.replaceDB(db),
		PMOCollaborator:          q.PMOCollaborator.replaceDB(db),
		PMOComment:               q.PMOComment.replaceDB(db),
		PMOCommentVersion:        q.PMOCommentVersion.replaceDB(db),
		PMOCompetitor:            q.PMOCompetitor.replaceDB(db),
		PMOContact:               q.PMOContact.replaceDB(db),
		PMOContractInfo:          q.PMOContractInfo.replaceDB(db),
		PMOContractInfoVersion:   q.PMOContractInfoVersion.replaceDB(db),
		PMODocumentGroup:         q.PMODocumentGroup.replaceDB(db),
		PMODocumentItem:          q.PMODocumentItem.replaceDB(db),
		PMODocumentItemVersion:   q.PMODocumentItemVersion.replaceDB(db),
		PMOLGInfo:                q.PMOLGInfo.replaceDB(db),
		PMOLGInfoVersion:         q.PMOLGInfoVersion.replaceDB(db),
		PMOPartner:               q.PMOPartner.replaceDB(db),
		PMOProject:               q.PMOProject.replaceDB(db),
		PMORemark:                q.PMORemark.replaceDB(db),
		PMORemarkVersion:         q.PMORemarkVersion.replaceDB(db),
		PMOTemplateChecklistItem: q.PMOTemplateChecklistItem.replaceDB(db),
		PMOTemplateDocument:      q.PMOTemplateDocument.replaceDB(db),
		PMOVendorItem:            q.PMOVendorItem.replaceDB(db),
		Project:                  q.Project.replaceDB(db),
		Sga:                      q.Sga.replaceDB(db),
		Team:                     q.Team.replaceDB(db),
		Timesheet:                q.Timesheet.replaceDB(db),
		User:                     q.User.replaceDB(db),
		UserAccessLevel:          q.UserAccessLevel.replaceDB(db),
	}
}

type queryCtx struct {
	Checkin                  ICheckinDo
	Department               IDepartmentDo
	Holiday                  IHolidayDo
	Ministry                 IMinistryDo
	PMOBidbondInfo           IPMOBidbondInfoDo
	PMOBidbondInfoVersion    IPMOBidbondInfoVersionDo
	PMOBiddingInfo           IPMOBiddingInfoDo
	PMOBiddingInfoVersion    IPMOBiddingInfoVersionDo
	PMOBudgetInfo            IPMOBudgetInfoDo
	PMOBudgetInfoVersion     IPMOBudgetInfoVersionDo
	PMOChecklistItem         IPMOChecklistItemDo
	PMOCollaborator          IPMOCollaboratorDo
	PMOComment               IPMOCommentDo
	PMOCommentVersion        IPMOCommentVersionDo
	PMOCompetitor            IPMOCompetitorDo
	PMOContact               IPMOContactDo
	PMOContractInfo          IPMOContractInfoDo
	PMOContractInfoVersion   IPMOContractInfoVersionDo
	PMODocumentGroup         IPMODocumentGroupDo
	PMODocumentItem          IPMODocumentItemDo
	PMODocumentItemVersion   IPMODocumentItemVersionDo
	PMOLGInfo                IPMOLGInfoDo
	PMOLGInfoVersion         IPMOLGInfoVersionDo
	PMOPartner               IPMOPartnerDo
	PMOProject               IPMOProjectDo
	PMORemark                IPMORemarkDo
	PMORemarkVersion         IPMORemarkVersionDo
	PMOTemplateChecklistItem IPMOTemplateChecklistItemDo
	PMOTemplateDocument      IPMOTemplateDocumentDo
	PMOVendorItem            IPMOVendorItemDo
	Project                  IProjectDo
	Sga                      ISgaDo
	Team                     ITeamDo
	Timesheet                ITimesheetDo
	User                     IUserDo
	UserAccessLevel          IUserAccessLevelDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Checkin:                  q.Checkin.WithContext(ctx),
		Department:               q.Department.WithContext(ctx),
		Holiday:                  q.Holiday.WithContext(ctx),
		Ministry:                 q.Ministry.WithContext(ctx),
		PMOBidbondInfo:           q.PMOBidbondInfo.WithContext(ctx),
		PMOBidbondInfoVersion:    q.PMOBidbondInfoVersion.WithContext(ctx),
		PMOBiddingInfo:           q.PMOBiddingInfo.WithContext(ctx),
		PMOBiddingInfoVersion:    q.PMOBiddingInfoVersion.WithContext(ctx),
		PMOBudgetInfo:            q.PMOBudgetInfo.WithContext(ctx),
		PMOBudgetInfoVersion:     q.PMOBudgetInfoVersion.WithContext(ctx),
		PMOChecklistItem:         q.PMOChecklistItem.WithContext(ctx),
		PMOCollaborator:          q.PMOCollaborator.WithContext(ctx),
		PMOComment:               q.PMOComment.WithContext(ctx),
		PMOCommentVersion:        q.PMOCommentVersion.WithContext(ctx),
		PMOCompetitor:            q.PMOCompetitor.WithContext(ctx),
		PMOContact:               q.PMOContact.WithContext(ctx),
		PMOContractInfo:          q.PMOContractInfo.WithContext(ctx),
		PMOContractInfoVersion:   q.PMOContractInfoVersion.WithContext(ctx),
		PMODocumentGroup:         q.PMODocumentGroup.WithContext(ctx),
		PMODocumentItem:          q.PMODocumentItem.WithContext(ctx),
		PMODocumentItemVersion:   q.PMODocumentItemVersion.WithContext(ctx),
		PMOLGInfo:                q.PMOLGInfo.WithContext(ctx),
		PMOLGInfoVersion:         q.PMOLGInfoVersion.WithContext(ctx),
		PMOPartner:               q.PMOPartner.WithContext(ctx),
		PMOProject:               q.PMOProject.WithContext(ctx),
		PMORemark:                q.PMORemark.WithContext(ctx),
		PMORemarkVersion:         q.PMORemarkVersion.WithContext(ctx),
		PMOTemplateChecklistItem: q.PMOTemplateChecklistItem.WithContext(ctx),
		PMOTemplateDocument:      q.PMOTemplateDocument.WithContext(ctx),
		PMOVendorItem:            q.PMOVendorItem.WithContext(ctx),
		Project:                  q.Project.WithContext(ctx),
		Sga:                      q.Sga.WithContext(ctx),
		Team:                     q.Team.WithContext(ctx),
		Timesheet:                q.Timesheet.WithContext(ctx),
		User:                     q.User.WithContext(ctx),
		UserAccessLevel:          q.UserAccessLevel.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
