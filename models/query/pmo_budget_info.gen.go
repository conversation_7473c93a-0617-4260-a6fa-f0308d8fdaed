// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBudgetInfo(db *gorm.DB, opts ...gen.DOOption) pMOBudgetInfo {
	_pMOBudgetInfo := pMOBudgetInfo{}

	_pMOBudgetInfo.pMOBudgetInfoDo.UseDB(db, opts...)
	_pMOBudgetInfo.pMOBudgetInfoDo.UseModel(&models.PMOBudgetInfo{})

	tableName := _pMOBudgetInfo.pMOBudgetInfoDo.TableName()
	_pMOBudgetInfo.ALL = field.NewAsterisk(tableName)
	_pMOBudgetInfo.ID = field.NewString(tableName, "id")
	_pMOBudgetInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBudgetInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBudgetInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBudgetInfo.ProjectID = field.NewString(tableName, "project_id")
	_pMOBudgetInfo.FundType = field.NewString(tableName, "fund_type")
	_pMOBudgetInfo.ProjectValue = field.NewFloat64(tableName, "project_value")
	_pMOBudgetInfo.BidbondValue = field.NewFloat64(tableName, "bidbond_value")
	_pMOBudgetInfo.Partner = field.NewString(tableName, "partner")
	_pMOBudgetInfo.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBudgetInfo.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBudgetInfo.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBudgetInfo.Project = pMOBudgetInfoHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBudgetInfo.CreatedBy = pMOBudgetInfoBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBudgetInfo.UpdatedBy = pMOBudgetInfoBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBudgetInfo.fillFieldMap()

	return _pMOBudgetInfo
}

type pMOBudgetInfo struct {
	pMOBudgetInfoDo

	ALL          field.Asterisk
	ID           field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ProjectID    field.String
	FundType     field.String
	ProjectValue field.Float64
	BidbondValue field.Float64
	Partner      field.String
	CreatedByID  field.String
	UpdatedByID  field.String
	DeletedByID  field.String
	Project      pMOBudgetInfoHasOneProject

	CreatedBy pMOBudgetInfoBelongsToCreatedBy

	UpdatedBy pMOBudgetInfoBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBudgetInfo) Table(newTableName string) *pMOBudgetInfo {
	p.pMOBudgetInfoDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBudgetInfo) As(alias string) *pMOBudgetInfo {
	p.pMOBudgetInfoDo.DO = *(p.pMOBudgetInfoDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBudgetInfo) updateTableName(table string) *pMOBudgetInfo {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.FundType = field.NewString(table, "fund_type")
	p.ProjectValue = field.NewFloat64(table, "project_value")
	p.BidbondValue = field.NewFloat64(table, "bidbond_value")
	p.Partner = field.NewString(table, "partner")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBudgetInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBudgetInfo) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 15)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["fund_type"] = p.FundType
	p.fieldMap["project_value"] = p.ProjectValue
	p.fieldMap["bidbond_value"] = p.BidbondValue
	p.fieldMap["partner"] = p.Partner
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOBudgetInfo) clone(db *gorm.DB) pMOBudgetInfo {
	p.pMOBudgetInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBudgetInfo) replaceDB(db *gorm.DB) pMOBudgetInfo {
	p.pMOBudgetInfoDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBudgetInfoHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBudgetInfoHasOneProject) Where(conds ...field.Expr) *pMOBudgetInfoHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoHasOneProject) WithContext(ctx context.Context) *pMOBudgetInfoHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoHasOneProject) Session(session *gorm.Session) *pMOBudgetInfoHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoHasOneProject) Model(m *models.PMOBudgetInfo) *pMOBudgetInfoHasOneProjectTx {
	return &pMOBudgetInfoHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoHasOneProject) Unscoped() *pMOBudgetInfoHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoHasOneProjectTx) Unscoped() *pMOBudgetInfoHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBudgetInfoBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBudgetInfoBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBudgetInfoBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoBelongsToCreatedBy) Session(session *gorm.Session) *pMOBudgetInfoBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoBelongsToCreatedBy) Model(m *models.PMOBudgetInfo) *pMOBudgetInfoBelongsToCreatedByTx {
	return &pMOBudgetInfoBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoBelongsToCreatedBy) Unscoped() *pMOBudgetInfoBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoBelongsToCreatedByTx) Unscoped() *pMOBudgetInfoBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBudgetInfoBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBudgetInfoBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBudgetInfoBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBudgetInfoBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoBelongsToUpdatedBy) Model(m *models.PMOBudgetInfo) *pMOBudgetInfoBelongsToUpdatedByTx {
	return &pMOBudgetInfoBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoBelongsToUpdatedBy) Unscoped() *pMOBudgetInfoBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoBelongsToUpdatedByTx) Unscoped() *pMOBudgetInfoBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoDo struct{ gen.DO }

type IPMOBudgetInfoDo interface {
	gen.SubQuery
	Debug() IPMOBudgetInfoDo
	WithContext(ctx context.Context) IPMOBudgetInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBudgetInfoDo
	WriteDB() IPMOBudgetInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBudgetInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBudgetInfoDo
	Not(conds ...gen.Condition) IPMOBudgetInfoDo
	Or(conds ...gen.Condition) IPMOBudgetInfoDo
	Select(conds ...field.Expr) IPMOBudgetInfoDo
	Where(conds ...gen.Condition) IPMOBudgetInfoDo
	Order(conds ...field.Expr) IPMOBudgetInfoDo
	Distinct(cols ...field.Expr) IPMOBudgetInfoDo
	Omit(cols ...field.Expr) IPMOBudgetInfoDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo
	Group(cols ...field.Expr) IPMOBudgetInfoDo
	Having(conds ...gen.Condition) IPMOBudgetInfoDo
	Limit(limit int) IPMOBudgetInfoDo
	Offset(offset int) IPMOBudgetInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBudgetInfoDo
	Unscoped() IPMOBudgetInfoDo
	Create(values ...*models.PMOBudgetInfo) error
	CreateInBatches(values []*models.PMOBudgetInfo, batchSize int) error
	Save(values ...*models.PMOBudgetInfo) error
	First() (*models.PMOBudgetInfo, error)
	Take() (*models.PMOBudgetInfo, error)
	Last() (*models.PMOBudgetInfo, error)
	Find() ([]*models.PMOBudgetInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBudgetInfo, err error)
	FindInBatches(result *[]*models.PMOBudgetInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBudgetInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBudgetInfoDo
	Assign(attrs ...field.AssignExpr) IPMOBudgetInfoDo
	Joins(fields ...field.RelationField) IPMOBudgetInfoDo
	Preload(fields ...field.RelationField) IPMOBudgetInfoDo
	FirstOrInit() (*models.PMOBudgetInfo, error)
	FirstOrCreate() (*models.PMOBudgetInfo, error)
	FindByPage(offset int, limit int) (result []*models.PMOBudgetInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBudgetInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBudgetInfoDo) Debug() IPMOBudgetInfoDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBudgetInfoDo) WithContext(ctx context.Context) IPMOBudgetInfoDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBudgetInfoDo) ReadDB() IPMOBudgetInfoDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBudgetInfoDo) WriteDB() IPMOBudgetInfoDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBudgetInfoDo) Session(config *gorm.Session) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBudgetInfoDo) Clauses(conds ...clause.Expression) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBudgetInfoDo) Returning(value interface{}, columns ...string) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBudgetInfoDo) Not(conds ...gen.Condition) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBudgetInfoDo) Or(conds ...gen.Condition) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBudgetInfoDo) Select(conds ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBudgetInfoDo) Where(conds ...gen.Condition) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBudgetInfoDo) Order(conds ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBudgetInfoDo) Distinct(cols ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBudgetInfoDo) Omit(cols ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBudgetInfoDo) Join(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBudgetInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBudgetInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBudgetInfoDo) Group(cols ...field.Expr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBudgetInfoDo) Having(conds ...gen.Condition) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBudgetInfoDo) Limit(limit int) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBudgetInfoDo) Offset(offset int) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBudgetInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBudgetInfoDo) Unscoped() IPMOBudgetInfoDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBudgetInfoDo) Create(values ...*models.PMOBudgetInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBudgetInfoDo) CreateInBatches(values []*models.PMOBudgetInfo, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBudgetInfoDo) Save(values ...*models.PMOBudgetInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBudgetInfoDo) First() (*models.PMOBudgetInfo, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfo), nil
	}
}

func (p pMOBudgetInfoDo) Take() (*models.PMOBudgetInfo, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfo), nil
	}
}

func (p pMOBudgetInfoDo) Last() (*models.PMOBudgetInfo, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfo), nil
	}
}

func (p pMOBudgetInfoDo) Find() ([]*models.PMOBudgetInfo, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBudgetInfo), err
}

func (p pMOBudgetInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBudgetInfo, err error) {
	buf := make([]*models.PMOBudgetInfo, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBudgetInfoDo) FindInBatches(result *[]*models.PMOBudgetInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBudgetInfoDo) Attrs(attrs ...field.AssignExpr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBudgetInfoDo) Assign(attrs ...field.AssignExpr) IPMOBudgetInfoDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBudgetInfoDo) Joins(fields ...field.RelationField) IPMOBudgetInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBudgetInfoDo) Preload(fields ...field.RelationField) IPMOBudgetInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBudgetInfoDo) FirstOrInit() (*models.PMOBudgetInfo, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfo), nil
	}
}

func (p pMOBudgetInfoDo) FirstOrCreate() (*models.PMOBudgetInfo, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfo), nil
	}
}

func (p pMOBudgetInfoDo) FindByPage(offset int, limit int) (result []*models.PMOBudgetInfo, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBudgetInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBudgetInfoDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBudgetInfoDo) Delete(models ...*models.PMOBudgetInfo) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBudgetInfoDo) withDO(do gen.Dao) *pMOBudgetInfoDo {
	p.DO = *do.(*gen.DO)
	return p
}
