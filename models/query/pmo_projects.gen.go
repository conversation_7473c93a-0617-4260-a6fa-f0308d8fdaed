// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOProject(db *gorm.DB, opts ...gen.DOOption) pMOProject {
	_pMOProject := pMOProject{}

	_pMOProject.pMOProjectDo.UseDB(db, opts...)
	_pMOProject.pMOProjectDo.UseModel(&models.PMOProject{})

	tableName := _pMOProject.pMOProjectDo.TableName()
	_pMOProject.ALL = field.NewAsterisk(tableName)
	_pMOProject.ID = field.NewString(tableName, "id")
	_pMOProject.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOProject.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOProject.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOProject.Name = field.NewString(tableName, "name")
	_pMOProject.Slug = field.NewString(tableName, "slug")
	_pMOProject.Email = field.NewString(tableName, "email")
	_pMOProject.Tags = field.NewField(tableName, "tags")
	_pMOProject.Status = field.NewString(tableName, "status")
	_pMOProject.ProjectID = field.NewString(tableName, "project_id")
	_pMOProject.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOProject.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOProject.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOProject.Permission = pMOProjectHasOnePermission{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Permission", "models.PMOCollaborator"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Permission.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Permission.UpdatedBy", "models.User"),
		},
		User: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Permission.User", "models.User"),
		},
		Project: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Permission struct {
				field.RelationField
			}
			Collaborators struct {
				field.RelationField
			}
			Comments struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
				Replies struct {
					field.RelationField
				}
			}
			CommentVersions struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
				Replies struct {
					field.RelationField
				}
			}
			Remarks struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			RemarkVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			DocumentGroups struct {
				field.RelationField
				Project struct {
					field.RelationField
				}
				DocumentItems struct {
					field.RelationField
					CreatedBy struct {
						field.RelationField
					}
					UpdatedBy struct {
						field.RelationField
					}
					Group struct {
						field.RelationField
					}
					File struct {
						field.RelationField
					}
					Project struct {
						field.RelationField
					}
				}
			}
			DocumentItems struct {
				field.RelationField
			}
			DocumentItemVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Contacts struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Competitors struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Partners struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BudgetInfo struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BudgetInfoVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BiddingInfo struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BiddingInfoVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			ContractInfo struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			ContractInfoVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BidbondInfo struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			BidbondInfoVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			LGInfo struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			LGInfoVersions struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			VendorItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Permission.Project", "models.PMOProject"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.Project", "models.Project"),
			},
			Permission: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.Permission", "models.PMOCollaborator"),
			},
			Collaborators: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.Collaborators", "models.PMOCollaborator"),
			},
			Comments: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
				Replies struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.Comments", "models.PMOComment"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Comments.User", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Comments.Project", "models.PMOProject"),
				},
				Replies: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Comments.Replies", "models.PMOComment"),
				},
			},
			CommentVersions: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
				Replies struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.CommentVersions", "models.PMOCommentVersion"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.CommentVersions.User", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.CommentVersions.Project", "models.PMOProject"),
				},
				Replies: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.CommentVersions.Replies", "models.PMOComment"),
				},
			},
			Remarks: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.Remarks", "models.PMORemark"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Remarks.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Remarks.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Remarks.Project", "models.PMOProject"),
				},
			},
			RemarkVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.RemarkVersions", "models.PMORemarkVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.RemarkVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.RemarkVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.RemarkVersions.Project", "models.PMOProject"),
				},
			},
			DocumentGroups: struct {
				field.RelationField
				Project struct {
					field.RelationField
				}
				DocumentItems struct {
					field.RelationField
					CreatedBy struct {
						field.RelationField
					}
					UpdatedBy struct {
						field.RelationField
					}
					Group struct {
						field.RelationField
					}
					File struct {
						field.RelationField
					}
					Project struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("Permission.Project.DocumentGroups", "models.PMODocumentGroup"),
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentGroups.Project", "models.PMOProject"),
				},
				DocumentItems: struct {
					field.RelationField
					CreatedBy struct {
						field.RelationField
					}
					UpdatedBy struct {
						field.RelationField
					}
					Group struct {
						field.RelationField
					}
					File struct {
						field.RelationField
					}
					Project struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
					CreatedBy: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
					},
					UpdatedBy: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
					},
					Group: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
					},
					File: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems.File", "models.File"),
					},
					Project: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Permission.Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
					},
				},
			},
			DocumentItems: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Permission.Project.DocumentItems", "models.PMODocumentItem"),
			},
			DocumentItemVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentItemVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentItemVersions.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentItemVersions.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.DocumentItemVersions.Project", "models.PMOProject"),
				},
			},
			Contacts: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.Contacts", "models.PMOContact"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Contacts.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Contacts.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Contacts.Project", "models.PMOProject"),
				},
			},
			Competitors: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.Competitors", "models.PMOCompetitor"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Competitors.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Competitors.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Competitors.Project", "models.PMOProject"),
				},
			},
			Partners: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.Partners", "models.PMOPartner"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Partners.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Partners.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.Partners.Project", "models.PMOProject"),
				},
			},
			BudgetInfo: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BudgetInfo", "models.PMOBudgetInfo"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfo.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfo.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfo.Project", "models.PMOProject"),
				},
			},
			BudgetInfoVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfoVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfoVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BudgetInfoVersions.Project", "models.PMOProject"),
				},
			},
			BiddingInfo: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BiddingInfo", "models.PMOBiddingInfo"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfo.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfo.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfo.Project", "models.PMOProject"),
				},
			},
			BiddingInfoVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfoVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfoVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BiddingInfoVersions.Project", "models.PMOProject"),
				},
			},
			ContractInfo: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.ContractInfo", "models.PMOContractInfo"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfo.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfo.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfo.Project", "models.PMOProject"),
				},
			},
			ContractInfoVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfoVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfoVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.ContractInfoVersions.Project", "models.PMOProject"),
				},
			},
			BidbondInfo: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BidbondInfo", "models.PMOBidbondInfo"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfo.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfo.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfo.Project", "models.PMOProject"),
				},
			},
			BidbondInfoVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfoVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfoVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.BidbondInfoVersions.Project", "models.PMOProject"),
				},
			},
			LGInfo: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.LGInfo", "models.PMOLGInfo"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfo.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfo.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfo.Project", "models.PMOProject"),
				},
			},
			LGInfoVersions: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.LGInfoVersions", "models.PMOLGInfoVersion"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfoVersions.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfoVersions.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.LGInfoVersions.Project", "models.PMOProject"),
				},
			},
			VendorItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Permission.Project.VendorItems", "models.PMOVendorItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.VendorItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.VendorItems.UpdatedBy", "models.User"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Permission.Project.VendorItems.Project", "models.PMOProject"),
				},
			},
		},
	}

	_pMOProject.Collaborators = pMOProjectHasManyCollaborators{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Collaborators", "models.PMOCollaborator"),
	}

	_pMOProject.Comments = pMOProjectHasManyComments{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Comments", "models.PMOComment"),
	}

	_pMOProject.CommentVersions = pMOProjectHasManyCommentVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CommentVersions", "models.PMOCommentVersion"),
	}

	_pMOProject.Remarks = pMOProjectHasManyRemarks{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Remarks", "models.PMORemark"),
	}

	_pMOProject.RemarkVersions = pMOProjectHasManyRemarkVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("RemarkVersions", "models.PMORemarkVersion"),
	}

	_pMOProject.DocumentGroups = pMOProjectHasManyDocumentGroups{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DocumentGroups", "models.PMODocumentGroup"),
	}

	_pMOProject.DocumentItems = pMOProjectHasManyDocumentItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DocumentItems", "models.PMODocumentItem"),
	}

	_pMOProject.DocumentItemVersions = pMOProjectHasManyDocumentItemVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DocumentItemVersions", "models.PMODocumentItemVersion"),
	}

	_pMOProject.Contacts = pMOProjectHasManyContacts{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Contacts", "models.PMOContact"),
	}

	_pMOProject.Competitors = pMOProjectHasManyCompetitors{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Competitors", "models.PMOCompetitor"),
	}

	_pMOProject.Partners = pMOProjectHasManyPartners{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Partners", "models.PMOPartner"),
	}

	_pMOProject.BudgetInfo = pMOProjectHasManyBudgetInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BudgetInfo", "models.PMOBudgetInfo"),
	}

	_pMOProject.BudgetInfoVersions = pMOProjectHasManyBudgetInfoVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
	}

	_pMOProject.BiddingInfo = pMOProjectHasManyBiddingInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BiddingInfo", "models.PMOBiddingInfo"),
	}

	_pMOProject.BiddingInfoVersions = pMOProjectHasManyBiddingInfoVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
	}

	_pMOProject.ContractInfo = pMOProjectHasManyContractInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ContractInfo", "models.PMOContractInfo"),
	}

	_pMOProject.ContractInfoVersions = pMOProjectHasManyContractInfoVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ContractInfoVersions", "models.PMOContractInfoVersion"),
	}

	_pMOProject.BidbondInfo = pMOProjectHasManyBidbondInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BidbondInfo", "models.PMOBidbondInfo"),
	}

	_pMOProject.BidbondInfoVersions = pMOProjectHasManyBidbondInfoVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
	}

	_pMOProject.LGInfo = pMOProjectHasManyLGInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("LGInfo", "models.PMOLGInfo"),
	}

	_pMOProject.LGInfoVersions = pMOProjectHasManyLGInfoVersions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("LGInfoVersions", "models.PMOLGInfoVersion"),
	}

	_pMOProject.VendorItems = pMOProjectHasManyVendorItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("VendorItems", "models.PMOVendorItem"),
	}

	_pMOProject.CreatedBy = pMOProjectBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOProject.UpdatedBy = pMOProjectBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOProject.Project = pMOProjectBelongsToProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.Project"),
	}

	_pMOProject.fillFieldMap()

	return _pMOProject
}

type pMOProject struct {
	pMOProjectDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Name        field.String
	Slug        field.String
	Email       field.String
	Tags        field.Field
	Status      field.String
	ProjectID   field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	Permission  pMOProjectHasOnePermission

	Collaborators pMOProjectHasManyCollaborators

	Comments pMOProjectHasManyComments

	CommentVersions pMOProjectHasManyCommentVersions

	Remarks pMOProjectHasManyRemarks

	RemarkVersions pMOProjectHasManyRemarkVersions

	DocumentGroups pMOProjectHasManyDocumentGroups

	DocumentItems pMOProjectHasManyDocumentItems

	DocumentItemVersions pMOProjectHasManyDocumentItemVersions

	Contacts pMOProjectHasManyContacts

	Competitors pMOProjectHasManyCompetitors

	Partners pMOProjectHasManyPartners

	BudgetInfo pMOProjectHasManyBudgetInfo

	BudgetInfoVersions pMOProjectHasManyBudgetInfoVersions

	BiddingInfo pMOProjectHasManyBiddingInfo

	BiddingInfoVersions pMOProjectHasManyBiddingInfoVersions

	ContractInfo pMOProjectHasManyContractInfo

	ContractInfoVersions pMOProjectHasManyContractInfoVersions

	BidbondInfo pMOProjectHasManyBidbondInfo

	BidbondInfoVersions pMOProjectHasManyBidbondInfoVersions

	LGInfo pMOProjectHasManyLGInfo

	LGInfoVersions pMOProjectHasManyLGInfoVersions

	VendorItems pMOProjectHasManyVendorItems

	CreatedBy pMOProjectBelongsToCreatedBy

	UpdatedBy pMOProjectBelongsToUpdatedBy

	Project pMOProjectBelongsToProject

	fieldMap map[string]field.Expr
}

func (p pMOProject) Table(newTableName string) *pMOProject {
	p.pMOProjectDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOProject) As(alias string) *pMOProject {
	p.pMOProjectDo.DO = *(p.pMOProjectDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOProject) updateTableName(table string) *pMOProject {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Name = field.NewString(table, "name")
	p.Slug = field.NewString(table, "slug")
	p.Email = field.NewString(table, "email")
	p.Tags = field.NewField(table, "tags")
	p.Status = field.NewString(table, "status")
	p.ProjectID = field.NewString(table, "project_id")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOProject) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOProject) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 39)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["name"] = p.Name
	p.fieldMap["slug"] = p.Slug
	p.fieldMap["email"] = p.Email
	p.fieldMap["tags"] = p.Tags
	p.fieldMap["status"] = p.Status
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOProject) clone(db *gorm.DB) pMOProject {
	p.pMOProjectDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Permission.db = db.Session(&gorm.Session{Initialized: true})
	p.Permission.db.Statement.ConnPool = db.Statement.ConnPool
	p.Collaborators.db = db.Session(&gorm.Session{Initialized: true})
	p.Collaborators.db.Statement.ConnPool = db.Statement.ConnPool
	p.Comments.db = db.Session(&gorm.Session{Initialized: true})
	p.Comments.db.Statement.ConnPool = db.Statement.ConnPool
	p.CommentVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.CommentVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.Remarks.db = db.Session(&gorm.Session{Initialized: true})
	p.Remarks.db.Statement.ConnPool = db.Statement.ConnPool
	p.RemarkVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.RemarkVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.DocumentGroups.db = db.Session(&gorm.Session{Initialized: true})
	p.DocumentGroups.db.Statement.ConnPool = db.Statement.ConnPool
	p.DocumentItems.db = db.Session(&gorm.Session{Initialized: true})
	p.DocumentItems.db.Statement.ConnPool = db.Statement.ConnPool
	p.DocumentItemVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.DocumentItemVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.Contacts.db = db.Session(&gorm.Session{Initialized: true})
	p.Contacts.db.Statement.ConnPool = db.Statement.ConnPool
	p.Competitors.db = db.Session(&gorm.Session{Initialized: true})
	p.Competitors.db.Statement.ConnPool = db.Statement.ConnPool
	p.Partners.db = db.Session(&gorm.Session{Initialized: true})
	p.Partners.db.Statement.ConnPool = db.Statement.ConnPool
	p.BudgetInfo.db = db.Session(&gorm.Session{Initialized: true})
	p.BudgetInfo.db.Statement.ConnPool = db.Statement.ConnPool
	p.BudgetInfoVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.BudgetInfoVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.BiddingInfo.db = db.Session(&gorm.Session{Initialized: true})
	p.BiddingInfo.db.Statement.ConnPool = db.Statement.ConnPool
	p.BiddingInfoVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.BiddingInfoVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.ContractInfo.db = db.Session(&gorm.Session{Initialized: true})
	p.ContractInfo.db.Statement.ConnPool = db.Statement.ConnPool
	p.ContractInfoVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.ContractInfoVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.BidbondInfo.db = db.Session(&gorm.Session{Initialized: true})
	p.BidbondInfo.db.Statement.ConnPool = db.Statement.ConnPool
	p.BidbondInfoVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.BidbondInfoVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.LGInfo.db = db.Session(&gorm.Session{Initialized: true})
	p.LGInfo.db.Statement.ConnPool = db.Statement.ConnPool
	p.LGInfoVersions.db = db.Session(&gorm.Session{Initialized: true})
	p.LGInfoVersions.db.Statement.ConnPool = db.Statement.ConnPool
	p.VendorItems.db = db.Session(&gorm.Session{Initialized: true})
	p.VendorItems.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOProject) replaceDB(db *gorm.DB) pMOProject {
	p.pMOProjectDo.ReplaceDB(db)
	p.Permission.db = db.Session(&gorm.Session{})
	p.Collaborators.db = db.Session(&gorm.Session{})
	p.Comments.db = db.Session(&gorm.Session{})
	p.CommentVersions.db = db.Session(&gorm.Session{})
	p.Remarks.db = db.Session(&gorm.Session{})
	p.RemarkVersions.db = db.Session(&gorm.Session{})
	p.DocumentGroups.db = db.Session(&gorm.Session{})
	p.DocumentItems.db = db.Session(&gorm.Session{})
	p.DocumentItemVersions.db = db.Session(&gorm.Session{})
	p.Contacts.db = db.Session(&gorm.Session{})
	p.Competitors.db = db.Session(&gorm.Session{})
	p.Partners.db = db.Session(&gorm.Session{})
	p.BudgetInfo.db = db.Session(&gorm.Session{})
	p.BudgetInfoVersions.db = db.Session(&gorm.Session{})
	p.BiddingInfo.db = db.Session(&gorm.Session{})
	p.BiddingInfoVersions.db = db.Session(&gorm.Session{})
	p.ContractInfo.db = db.Session(&gorm.Session{})
	p.ContractInfoVersions.db = db.Session(&gorm.Session{})
	p.BidbondInfo.db = db.Session(&gorm.Session{})
	p.BidbondInfoVersions.db = db.Session(&gorm.Session{})
	p.LGInfo.db = db.Session(&gorm.Session{})
	p.LGInfoVersions.db = db.Session(&gorm.Session{})
	p.VendorItems.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	p.Project.db = db.Session(&gorm.Session{})
	return p
}

type pMOProjectHasOnePermission struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	User struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Permission struct {
			field.RelationField
		}
		Collaborators struct {
			field.RelationField
		}
		Comments struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}
		CommentVersions struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}
		Remarks struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		RemarkVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		DocumentGroups struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}
		DocumentItems struct {
			field.RelationField
		}
		DocumentItemVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Contacts struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Competitors struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Partners struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BudgetInfo struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BudgetInfoVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BiddingInfo struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BiddingInfoVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		ContractInfo struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		ContractInfoVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BidbondInfo struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		BidbondInfoVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		LGInfo struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		LGInfoVersions struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		VendorItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
}

func (a pMOProjectHasOnePermission) Where(conds ...field.Expr) *pMOProjectHasOnePermission {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasOnePermission) WithContext(ctx context.Context) *pMOProjectHasOnePermission {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasOnePermission) Session(session *gorm.Session) *pMOProjectHasOnePermission {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasOnePermission) Model(m *models.PMOProject) *pMOProjectHasOnePermissionTx {
	return &pMOProjectHasOnePermissionTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasOnePermission) Unscoped() *pMOProjectHasOnePermission {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasOnePermissionTx struct{ tx *gorm.Association }

func (a pMOProjectHasOnePermissionTx) Find() (result *models.PMOCollaborator, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasOnePermissionTx) Append(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasOnePermissionTx) Replace(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasOnePermissionTx) Delete(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasOnePermissionTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasOnePermissionTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasOnePermissionTx) Unscoped() *pMOProjectHasOnePermissionTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyCollaborators struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyCollaborators) Where(conds ...field.Expr) *pMOProjectHasManyCollaborators {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyCollaborators) WithContext(ctx context.Context) *pMOProjectHasManyCollaborators {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyCollaborators) Session(session *gorm.Session) *pMOProjectHasManyCollaborators {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyCollaborators) Model(m *models.PMOProject) *pMOProjectHasManyCollaboratorsTx {
	return &pMOProjectHasManyCollaboratorsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyCollaborators) Unscoped() *pMOProjectHasManyCollaborators {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyCollaboratorsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyCollaboratorsTx) Find() (result []*models.PMOCollaborator, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyCollaboratorsTx) Append(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyCollaboratorsTx) Replace(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyCollaboratorsTx) Delete(values ...*models.PMOCollaborator) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyCollaboratorsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyCollaboratorsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyCollaboratorsTx) Unscoped() *pMOProjectHasManyCollaboratorsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyComments struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyComments) Where(conds ...field.Expr) *pMOProjectHasManyComments {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyComments) WithContext(ctx context.Context) *pMOProjectHasManyComments {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyComments) Session(session *gorm.Session) *pMOProjectHasManyComments {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyComments) Model(m *models.PMOProject) *pMOProjectHasManyCommentsTx {
	return &pMOProjectHasManyCommentsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyComments) Unscoped() *pMOProjectHasManyComments {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyCommentsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyCommentsTx) Find() (result []*models.PMOComment, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyCommentsTx) Append(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyCommentsTx) Replace(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyCommentsTx) Delete(values ...*models.PMOComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyCommentsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyCommentsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyCommentsTx) Unscoped() *pMOProjectHasManyCommentsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyCommentVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyCommentVersions) Where(conds ...field.Expr) *pMOProjectHasManyCommentVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyCommentVersions) WithContext(ctx context.Context) *pMOProjectHasManyCommentVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyCommentVersions) Session(session *gorm.Session) *pMOProjectHasManyCommentVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyCommentVersions) Model(m *models.PMOProject) *pMOProjectHasManyCommentVersionsTx {
	return &pMOProjectHasManyCommentVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyCommentVersions) Unscoped() *pMOProjectHasManyCommentVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyCommentVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyCommentVersionsTx) Find() (result []*models.PMOCommentVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyCommentVersionsTx) Append(values ...*models.PMOCommentVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyCommentVersionsTx) Replace(values ...*models.PMOCommentVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyCommentVersionsTx) Delete(values ...*models.PMOCommentVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyCommentVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyCommentVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyCommentVersionsTx) Unscoped() *pMOProjectHasManyCommentVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyRemarks struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyRemarks) Where(conds ...field.Expr) *pMOProjectHasManyRemarks {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyRemarks) WithContext(ctx context.Context) *pMOProjectHasManyRemarks {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyRemarks) Session(session *gorm.Session) *pMOProjectHasManyRemarks {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyRemarks) Model(m *models.PMOProject) *pMOProjectHasManyRemarksTx {
	return &pMOProjectHasManyRemarksTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyRemarks) Unscoped() *pMOProjectHasManyRemarks {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyRemarksTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyRemarksTx) Find() (result []*models.PMORemark, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyRemarksTx) Append(values ...*models.PMORemark) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyRemarksTx) Replace(values ...*models.PMORemark) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyRemarksTx) Delete(values ...*models.PMORemark) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyRemarksTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyRemarksTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyRemarksTx) Unscoped() *pMOProjectHasManyRemarksTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyRemarkVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyRemarkVersions) Where(conds ...field.Expr) *pMOProjectHasManyRemarkVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyRemarkVersions) WithContext(ctx context.Context) *pMOProjectHasManyRemarkVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyRemarkVersions) Session(session *gorm.Session) *pMOProjectHasManyRemarkVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyRemarkVersions) Model(m *models.PMOProject) *pMOProjectHasManyRemarkVersionsTx {
	return &pMOProjectHasManyRemarkVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyRemarkVersions) Unscoped() *pMOProjectHasManyRemarkVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyRemarkVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyRemarkVersionsTx) Find() (result []*models.PMORemarkVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyRemarkVersionsTx) Append(values ...*models.PMORemarkVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyRemarkVersionsTx) Replace(values ...*models.PMORemarkVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyRemarkVersionsTx) Delete(values ...*models.PMORemarkVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyRemarkVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyRemarkVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyRemarkVersionsTx) Unscoped() *pMOProjectHasManyRemarkVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentGroups struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyDocumentGroups) Where(conds ...field.Expr) *pMOProjectHasManyDocumentGroups {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyDocumentGroups) WithContext(ctx context.Context) *pMOProjectHasManyDocumentGroups {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyDocumentGroups) Session(session *gorm.Session) *pMOProjectHasManyDocumentGroups {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyDocumentGroups) Model(m *models.PMOProject) *pMOProjectHasManyDocumentGroupsTx {
	return &pMOProjectHasManyDocumentGroupsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyDocumentGroups) Unscoped() *pMOProjectHasManyDocumentGroups {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentGroupsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyDocumentGroupsTx) Find() (result []*models.PMODocumentGroup, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyDocumentGroupsTx) Append(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyDocumentGroupsTx) Replace(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyDocumentGroupsTx) Delete(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyDocumentGroupsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyDocumentGroupsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyDocumentGroupsTx) Unscoped() *pMOProjectHasManyDocumentGroupsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentItems struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyDocumentItems) Where(conds ...field.Expr) *pMOProjectHasManyDocumentItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyDocumentItems) WithContext(ctx context.Context) *pMOProjectHasManyDocumentItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyDocumentItems) Session(session *gorm.Session) *pMOProjectHasManyDocumentItems {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyDocumentItems) Model(m *models.PMOProject) *pMOProjectHasManyDocumentItemsTx {
	return &pMOProjectHasManyDocumentItemsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyDocumentItems) Unscoped() *pMOProjectHasManyDocumentItems {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentItemsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyDocumentItemsTx) Find() (result []*models.PMODocumentItem, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyDocumentItemsTx) Append(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyDocumentItemsTx) Replace(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyDocumentItemsTx) Delete(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyDocumentItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyDocumentItemsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyDocumentItemsTx) Unscoped() *pMOProjectHasManyDocumentItemsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentItemVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyDocumentItemVersions) Where(conds ...field.Expr) *pMOProjectHasManyDocumentItemVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyDocumentItemVersions) WithContext(ctx context.Context) *pMOProjectHasManyDocumentItemVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyDocumentItemVersions) Session(session *gorm.Session) *pMOProjectHasManyDocumentItemVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyDocumentItemVersions) Model(m *models.PMOProject) *pMOProjectHasManyDocumentItemVersionsTx {
	return &pMOProjectHasManyDocumentItemVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyDocumentItemVersions) Unscoped() *pMOProjectHasManyDocumentItemVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyDocumentItemVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyDocumentItemVersionsTx) Find() (result []*models.PMODocumentItemVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Append(values ...*models.PMODocumentItemVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Replace(values ...*models.PMODocumentItemVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Delete(values ...*models.PMODocumentItemVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyDocumentItemVersionsTx) Unscoped() *pMOProjectHasManyDocumentItemVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyContacts struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyContacts) Where(conds ...field.Expr) *pMOProjectHasManyContacts {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyContacts) WithContext(ctx context.Context) *pMOProjectHasManyContacts {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyContacts) Session(session *gorm.Session) *pMOProjectHasManyContacts {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyContacts) Model(m *models.PMOProject) *pMOProjectHasManyContactsTx {
	return &pMOProjectHasManyContactsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyContacts) Unscoped() *pMOProjectHasManyContacts {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyContactsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyContactsTx) Find() (result []*models.PMOContact, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyContactsTx) Append(values ...*models.PMOContact) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyContactsTx) Replace(values ...*models.PMOContact) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyContactsTx) Delete(values ...*models.PMOContact) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyContactsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyContactsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyContactsTx) Unscoped() *pMOProjectHasManyContactsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyCompetitors struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyCompetitors) Where(conds ...field.Expr) *pMOProjectHasManyCompetitors {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyCompetitors) WithContext(ctx context.Context) *pMOProjectHasManyCompetitors {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyCompetitors) Session(session *gorm.Session) *pMOProjectHasManyCompetitors {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyCompetitors) Model(m *models.PMOProject) *pMOProjectHasManyCompetitorsTx {
	return &pMOProjectHasManyCompetitorsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyCompetitors) Unscoped() *pMOProjectHasManyCompetitors {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyCompetitorsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyCompetitorsTx) Find() (result []*models.PMOCompetitor, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyCompetitorsTx) Append(values ...*models.PMOCompetitor) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyCompetitorsTx) Replace(values ...*models.PMOCompetitor) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyCompetitorsTx) Delete(values ...*models.PMOCompetitor) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyCompetitorsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyCompetitorsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyCompetitorsTx) Unscoped() *pMOProjectHasManyCompetitorsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyPartners struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyPartners) Where(conds ...field.Expr) *pMOProjectHasManyPartners {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyPartners) WithContext(ctx context.Context) *pMOProjectHasManyPartners {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyPartners) Session(session *gorm.Session) *pMOProjectHasManyPartners {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyPartners) Model(m *models.PMOProject) *pMOProjectHasManyPartnersTx {
	return &pMOProjectHasManyPartnersTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyPartners) Unscoped() *pMOProjectHasManyPartners {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyPartnersTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyPartnersTx) Find() (result []*models.PMOPartner, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyPartnersTx) Append(values ...*models.PMOPartner) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyPartnersTx) Replace(values ...*models.PMOPartner) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyPartnersTx) Delete(values ...*models.PMOPartner) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyPartnersTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyPartnersTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyPartnersTx) Unscoped() *pMOProjectHasManyPartnersTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBudgetInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBudgetInfo) Where(conds ...field.Expr) *pMOProjectHasManyBudgetInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBudgetInfo) WithContext(ctx context.Context) *pMOProjectHasManyBudgetInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBudgetInfo) Session(session *gorm.Session) *pMOProjectHasManyBudgetInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBudgetInfo) Model(m *models.PMOProject) *pMOProjectHasManyBudgetInfoTx {
	return &pMOProjectHasManyBudgetInfoTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBudgetInfo) Unscoped() *pMOProjectHasManyBudgetInfo {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBudgetInfoTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBudgetInfoTx) Find() (result []*models.PMOBudgetInfo, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBudgetInfoTx) Append(values ...*models.PMOBudgetInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoTx) Replace(values ...*models.PMOBudgetInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoTx) Delete(values ...*models.PMOBudgetInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBudgetInfoTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBudgetInfoTx) Unscoped() *pMOProjectHasManyBudgetInfoTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBudgetInfoVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBudgetInfoVersions) Where(conds ...field.Expr) *pMOProjectHasManyBudgetInfoVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBudgetInfoVersions) WithContext(ctx context.Context) *pMOProjectHasManyBudgetInfoVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBudgetInfoVersions) Session(session *gorm.Session) *pMOProjectHasManyBudgetInfoVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBudgetInfoVersions) Model(m *models.PMOProject) *pMOProjectHasManyBudgetInfoVersionsTx {
	return &pMOProjectHasManyBudgetInfoVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBudgetInfoVersions) Unscoped() *pMOProjectHasManyBudgetInfoVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBudgetInfoVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBudgetInfoVersionsTx) Find() (result []*models.PMOBudgetInfoVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Append(values ...*models.PMOBudgetInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Replace(values ...*models.PMOBudgetInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Delete(values ...*models.PMOBudgetInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBudgetInfoVersionsTx) Unscoped() *pMOProjectHasManyBudgetInfoVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBiddingInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBiddingInfo) Where(conds ...field.Expr) *pMOProjectHasManyBiddingInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBiddingInfo) WithContext(ctx context.Context) *pMOProjectHasManyBiddingInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBiddingInfo) Session(session *gorm.Session) *pMOProjectHasManyBiddingInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBiddingInfo) Model(m *models.PMOProject) *pMOProjectHasManyBiddingInfoTx {
	return &pMOProjectHasManyBiddingInfoTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBiddingInfo) Unscoped() *pMOProjectHasManyBiddingInfo {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBiddingInfoTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBiddingInfoTx) Find() (result []*models.PMOBiddingInfo, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBiddingInfoTx) Append(values ...*models.PMOBiddingInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoTx) Replace(values ...*models.PMOBiddingInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoTx) Delete(values ...*models.PMOBiddingInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBiddingInfoTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBiddingInfoTx) Unscoped() *pMOProjectHasManyBiddingInfoTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBiddingInfoVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBiddingInfoVersions) Where(conds ...field.Expr) *pMOProjectHasManyBiddingInfoVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBiddingInfoVersions) WithContext(ctx context.Context) *pMOProjectHasManyBiddingInfoVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBiddingInfoVersions) Session(session *gorm.Session) *pMOProjectHasManyBiddingInfoVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBiddingInfoVersions) Model(m *models.PMOProject) *pMOProjectHasManyBiddingInfoVersionsTx {
	return &pMOProjectHasManyBiddingInfoVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBiddingInfoVersions) Unscoped() *pMOProjectHasManyBiddingInfoVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBiddingInfoVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBiddingInfoVersionsTx) Find() (result []*models.PMOBiddingInfoVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Append(values ...*models.PMOBiddingInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Replace(values ...*models.PMOBiddingInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Delete(values ...*models.PMOBiddingInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBiddingInfoVersionsTx) Unscoped() *pMOProjectHasManyBiddingInfoVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyContractInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyContractInfo) Where(conds ...field.Expr) *pMOProjectHasManyContractInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyContractInfo) WithContext(ctx context.Context) *pMOProjectHasManyContractInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyContractInfo) Session(session *gorm.Session) *pMOProjectHasManyContractInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyContractInfo) Model(m *models.PMOProject) *pMOProjectHasManyContractInfoTx {
	return &pMOProjectHasManyContractInfoTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyContractInfo) Unscoped() *pMOProjectHasManyContractInfo {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyContractInfoTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyContractInfoTx) Find() (result []*models.PMOContractInfo, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyContractInfoTx) Append(values ...*models.PMOContractInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyContractInfoTx) Replace(values ...*models.PMOContractInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyContractInfoTx) Delete(values ...*models.PMOContractInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyContractInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyContractInfoTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyContractInfoTx) Unscoped() *pMOProjectHasManyContractInfoTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyContractInfoVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyContractInfoVersions) Where(conds ...field.Expr) *pMOProjectHasManyContractInfoVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyContractInfoVersions) WithContext(ctx context.Context) *pMOProjectHasManyContractInfoVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyContractInfoVersions) Session(session *gorm.Session) *pMOProjectHasManyContractInfoVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyContractInfoVersions) Model(m *models.PMOProject) *pMOProjectHasManyContractInfoVersionsTx {
	return &pMOProjectHasManyContractInfoVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyContractInfoVersions) Unscoped() *pMOProjectHasManyContractInfoVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyContractInfoVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyContractInfoVersionsTx) Find() (result []*models.PMOContractInfoVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyContractInfoVersionsTx) Append(values ...*models.PMOContractInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyContractInfoVersionsTx) Replace(values ...*models.PMOContractInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyContractInfoVersionsTx) Delete(values ...*models.PMOContractInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyContractInfoVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyContractInfoVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyContractInfoVersionsTx) Unscoped() *pMOProjectHasManyContractInfoVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBidbondInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBidbondInfo) Where(conds ...field.Expr) *pMOProjectHasManyBidbondInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBidbondInfo) WithContext(ctx context.Context) *pMOProjectHasManyBidbondInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBidbondInfo) Session(session *gorm.Session) *pMOProjectHasManyBidbondInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBidbondInfo) Model(m *models.PMOProject) *pMOProjectHasManyBidbondInfoTx {
	return &pMOProjectHasManyBidbondInfoTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBidbondInfo) Unscoped() *pMOProjectHasManyBidbondInfo {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBidbondInfoTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBidbondInfoTx) Find() (result []*models.PMOBidbondInfo, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBidbondInfoTx) Append(values ...*models.PMOBidbondInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoTx) Replace(values ...*models.PMOBidbondInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoTx) Delete(values ...*models.PMOBidbondInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBidbondInfoTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBidbondInfoTx) Unscoped() *pMOProjectHasManyBidbondInfoTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyBidbondInfoVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyBidbondInfoVersions) Where(conds ...field.Expr) *pMOProjectHasManyBidbondInfoVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyBidbondInfoVersions) WithContext(ctx context.Context) *pMOProjectHasManyBidbondInfoVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyBidbondInfoVersions) Session(session *gorm.Session) *pMOProjectHasManyBidbondInfoVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyBidbondInfoVersions) Model(m *models.PMOProject) *pMOProjectHasManyBidbondInfoVersionsTx {
	return &pMOProjectHasManyBidbondInfoVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyBidbondInfoVersions) Unscoped() *pMOProjectHasManyBidbondInfoVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyBidbondInfoVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyBidbondInfoVersionsTx) Find() (result []*models.PMOBidbondInfoVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Append(values ...*models.PMOBidbondInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Replace(values ...*models.PMOBidbondInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Delete(values ...*models.PMOBidbondInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyBidbondInfoVersionsTx) Unscoped() *pMOProjectHasManyBidbondInfoVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyLGInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyLGInfo) Where(conds ...field.Expr) *pMOProjectHasManyLGInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyLGInfo) WithContext(ctx context.Context) *pMOProjectHasManyLGInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyLGInfo) Session(session *gorm.Session) *pMOProjectHasManyLGInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyLGInfo) Model(m *models.PMOProject) *pMOProjectHasManyLGInfoTx {
	return &pMOProjectHasManyLGInfoTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyLGInfo) Unscoped() *pMOProjectHasManyLGInfo {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyLGInfoTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyLGInfoTx) Find() (result []*models.PMOLGInfo, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyLGInfoTx) Append(values ...*models.PMOLGInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyLGInfoTx) Replace(values ...*models.PMOLGInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyLGInfoTx) Delete(values ...*models.PMOLGInfo) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyLGInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyLGInfoTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyLGInfoTx) Unscoped() *pMOProjectHasManyLGInfoTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyLGInfoVersions struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyLGInfoVersions) Where(conds ...field.Expr) *pMOProjectHasManyLGInfoVersions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyLGInfoVersions) WithContext(ctx context.Context) *pMOProjectHasManyLGInfoVersions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyLGInfoVersions) Session(session *gorm.Session) *pMOProjectHasManyLGInfoVersions {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyLGInfoVersions) Model(m *models.PMOProject) *pMOProjectHasManyLGInfoVersionsTx {
	return &pMOProjectHasManyLGInfoVersionsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyLGInfoVersions) Unscoped() *pMOProjectHasManyLGInfoVersions {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyLGInfoVersionsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyLGInfoVersionsTx) Find() (result []*models.PMOLGInfoVersion, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyLGInfoVersionsTx) Append(values ...*models.PMOLGInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyLGInfoVersionsTx) Replace(values ...*models.PMOLGInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyLGInfoVersionsTx) Delete(values ...*models.PMOLGInfoVersion) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyLGInfoVersionsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyLGInfoVersionsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyLGInfoVersionsTx) Unscoped() *pMOProjectHasManyLGInfoVersionsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectHasManyVendorItems struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectHasManyVendorItems) Where(conds ...field.Expr) *pMOProjectHasManyVendorItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectHasManyVendorItems) WithContext(ctx context.Context) *pMOProjectHasManyVendorItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectHasManyVendorItems) Session(session *gorm.Session) *pMOProjectHasManyVendorItems {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectHasManyVendorItems) Model(m *models.PMOProject) *pMOProjectHasManyVendorItemsTx {
	return &pMOProjectHasManyVendorItemsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectHasManyVendorItems) Unscoped() *pMOProjectHasManyVendorItems {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectHasManyVendorItemsTx struct{ tx *gorm.Association }

func (a pMOProjectHasManyVendorItemsTx) Find() (result []*models.PMOVendorItem, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectHasManyVendorItemsTx) Append(values ...*models.PMOVendorItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectHasManyVendorItemsTx) Replace(values ...*models.PMOVendorItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectHasManyVendorItemsTx) Delete(values ...*models.PMOVendorItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectHasManyVendorItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectHasManyVendorItemsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectHasManyVendorItemsTx) Unscoped() *pMOProjectHasManyVendorItemsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectBelongsToCreatedBy) Where(conds ...field.Expr) *pMOProjectBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectBelongsToCreatedBy) WithContext(ctx context.Context) *pMOProjectBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectBelongsToCreatedBy) Session(session *gorm.Session) *pMOProjectBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectBelongsToCreatedBy) Model(m *models.PMOProject) *pMOProjectBelongsToCreatedByTx {
	return &pMOProjectBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectBelongsToCreatedBy) Unscoped() *pMOProjectBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOProjectBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectBelongsToCreatedByTx) Unscoped() *pMOProjectBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOProjectBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOProjectBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectBelongsToUpdatedBy) Session(session *gorm.Session) *pMOProjectBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectBelongsToUpdatedBy) Model(m *models.PMOProject) *pMOProjectBelongsToUpdatedByTx {
	return &pMOProjectBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectBelongsToUpdatedBy) Unscoped() *pMOProjectBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOProjectBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectBelongsToUpdatedByTx) Unscoped() *pMOProjectBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectBelongsToProject struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOProjectBelongsToProject) Where(conds ...field.Expr) *pMOProjectBelongsToProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOProjectBelongsToProject) WithContext(ctx context.Context) *pMOProjectBelongsToProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOProjectBelongsToProject) Session(session *gorm.Session) *pMOProjectBelongsToProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOProjectBelongsToProject) Model(m *models.PMOProject) *pMOProjectBelongsToProjectTx {
	return &pMOProjectBelongsToProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOProjectBelongsToProject) Unscoped() *pMOProjectBelongsToProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOProjectBelongsToProjectTx struct{ tx *gorm.Association }

func (a pMOProjectBelongsToProjectTx) Find() (result *models.Project, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOProjectBelongsToProjectTx) Append(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOProjectBelongsToProjectTx) Replace(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOProjectBelongsToProjectTx) Delete(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOProjectBelongsToProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOProjectBelongsToProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOProjectBelongsToProjectTx) Unscoped() *pMOProjectBelongsToProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOProjectDo struct{ gen.DO }

type IPMOProjectDo interface {
	gen.SubQuery
	Debug() IPMOProjectDo
	WithContext(ctx context.Context) IPMOProjectDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOProjectDo
	WriteDB() IPMOProjectDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOProjectDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOProjectDo
	Not(conds ...gen.Condition) IPMOProjectDo
	Or(conds ...gen.Condition) IPMOProjectDo
	Select(conds ...field.Expr) IPMOProjectDo
	Where(conds ...gen.Condition) IPMOProjectDo
	Order(conds ...field.Expr) IPMOProjectDo
	Distinct(cols ...field.Expr) IPMOProjectDo
	Omit(cols ...field.Expr) IPMOProjectDo
	Join(table schema.Tabler, on ...field.Expr) IPMOProjectDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOProjectDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOProjectDo
	Group(cols ...field.Expr) IPMOProjectDo
	Having(conds ...gen.Condition) IPMOProjectDo
	Limit(limit int) IPMOProjectDo
	Offset(offset int) IPMOProjectDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOProjectDo
	Unscoped() IPMOProjectDo
	Create(values ...*models.PMOProject) error
	CreateInBatches(values []*models.PMOProject, batchSize int) error
	Save(values ...*models.PMOProject) error
	First() (*models.PMOProject, error)
	Take() (*models.PMOProject, error)
	Last() (*models.PMOProject, error)
	Find() ([]*models.PMOProject, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOProject, err error)
	FindInBatches(result *[]*models.PMOProject, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOProject) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOProjectDo
	Assign(attrs ...field.AssignExpr) IPMOProjectDo
	Joins(fields ...field.RelationField) IPMOProjectDo
	Preload(fields ...field.RelationField) IPMOProjectDo
	FirstOrInit() (*models.PMOProject, error)
	FirstOrCreate() (*models.PMOProject, error)
	FindByPage(offset int, limit int) (result []*models.PMOProject, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOProjectDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOProjectDo) Debug() IPMOProjectDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOProjectDo) WithContext(ctx context.Context) IPMOProjectDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOProjectDo) ReadDB() IPMOProjectDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOProjectDo) WriteDB() IPMOProjectDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOProjectDo) Session(config *gorm.Session) IPMOProjectDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOProjectDo) Clauses(conds ...clause.Expression) IPMOProjectDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOProjectDo) Returning(value interface{}, columns ...string) IPMOProjectDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOProjectDo) Not(conds ...gen.Condition) IPMOProjectDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOProjectDo) Or(conds ...gen.Condition) IPMOProjectDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOProjectDo) Select(conds ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOProjectDo) Where(conds ...gen.Condition) IPMOProjectDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOProjectDo) Order(conds ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOProjectDo) Distinct(cols ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOProjectDo) Omit(cols ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOProjectDo) Join(table schema.Tabler, on ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOProjectDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOProjectDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOProjectDo) Group(cols ...field.Expr) IPMOProjectDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOProjectDo) Having(conds ...gen.Condition) IPMOProjectDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOProjectDo) Limit(limit int) IPMOProjectDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOProjectDo) Offset(offset int) IPMOProjectDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOProjectDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOProjectDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOProjectDo) Unscoped() IPMOProjectDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOProjectDo) Create(values ...*models.PMOProject) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOProjectDo) CreateInBatches(values []*models.PMOProject, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOProjectDo) Save(values ...*models.PMOProject) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOProjectDo) First() (*models.PMOProject, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOProject), nil
	}
}

func (p pMOProjectDo) Take() (*models.PMOProject, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOProject), nil
	}
}

func (p pMOProjectDo) Last() (*models.PMOProject, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOProject), nil
	}
}

func (p pMOProjectDo) Find() ([]*models.PMOProject, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOProject), err
}

func (p pMOProjectDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOProject, err error) {
	buf := make([]*models.PMOProject, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOProjectDo) FindInBatches(result *[]*models.PMOProject, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOProjectDo) Attrs(attrs ...field.AssignExpr) IPMOProjectDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOProjectDo) Assign(attrs ...field.AssignExpr) IPMOProjectDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOProjectDo) Joins(fields ...field.RelationField) IPMOProjectDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOProjectDo) Preload(fields ...field.RelationField) IPMOProjectDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOProjectDo) FirstOrInit() (*models.PMOProject, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOProject), nil
	}
}

func (p pMOProjectDo) FirstOrCreate() (*models.PMOProject, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOProject), nil
	}
}

func (p pMOProjectDo) FindByPage(offset int, limit int) (result []*models.PMOProject, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOProjectDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOProjectDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOProjectDo) Delete(models ...*models.PMOProject) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOProjectDo) withDO(do gen.Dao) *pMOProjectDo {
	p.DO = *do.(*gen.DO)
	return p
}
