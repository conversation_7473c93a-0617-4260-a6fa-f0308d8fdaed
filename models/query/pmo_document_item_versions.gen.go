// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMODocumentItemVersion(db *gorm.DB, opts ...gen.DOOption) pMODocumentItemVersion {
	_pMODocumentItemVersion := pMODocumentItemVersion{}

	_pMODocumentItemVersion.pMODocumentItemVersionDo.UseDB(db, opts...)
	_pMODocumentItemVersion.pMODocumentItemVersionDo.UseModel(&models.PMODocumentItemVersion{})

	tableName := _pMODocumentItemVersion.pMODocumentItemVersionDo.TableName()
	_pMODocumentItemVersion.ALL = field.NewAsterisk(tableName)
	_pMODocumentItemVersion.ID = field.NewString(tableName, "id")
	_pMODocumentItemVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMODocumentItemVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMODocumentItemVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMODocumentItemVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMODocumentItemVersion.TabKey = field.NewString(tableName, "tab_key")
	_pMODocumentItemVersion.GroupID = field.NewString(tableName, "group_id")
	_pMODocumentItemVersion.Name = field.NewString(tableName, "name")
	_pMODocumentItemVersion.SharepointURL = field.NewString(tableName, "sharepoint_url")
	_pMODocumentItemVersion.Date = field.NewTime(tableName, "date")
	_pMODocumentItemVersion.Type = field.NewString(tableName, "type")
	_pMODocumentItemVersion.FileID = field.NewString(tableName, "file_id")
	_pMODocumentItemVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMODocumentItemVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMODocumentItemVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMODocumentItemVersion.OriginalID = field.NewString(tableName, "document_item_id")
	_pMODocumentItemVersion.Project = pMODocumentItemVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMODocumentItemVersion.CreatedBy = pMODocumentItemVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMODocumentItemVersion.UpdatedBy = pMODocumentItemVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMODocumentItemVersion.Group = pMODocumentItemVersionBelongsToGroup{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Group", "models.PMODocumentGroup"),
	}

	_pMODocumentItemVersion.File = pMODocumentItemVersionBelongsToFile{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("File", "models.File"),
	}

	_pMODocumentItemVersion.fillFieldMap()

	return _pMODocumentItemVersion
}

type pMODocumentItemVersion struct {
	pMODocumentItemVersionDo

	ALL           field.Asterisk
	ID            field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	ProjectID     field.String
	TabKey        field.String
	GroupID       field.String
	Name          field.String
	SharepointURL field.String
	Date          field.Time
	Type          field.String
	FileID        field.String
	CreatedByID   field.String
	UpdatedByID   field.String
	DeletedByID   field.String
	OriginalID    field.String
	Project       pMODocumentItemVersionHasOneProject

	CreatedBy pMODocumentItemVersionBelongsToCreatedBy

	UpdatedBy pMODocumentItemVersionBelongsToUpdatedBy

	Group pMODocumentItemVersionBelongsToGroup

	File pMODocumentItemVersionBelongsToFile

	fieldMap map[string]field.Expr
}

func (p pMODocumentItemVersion) Table(newTableName string) *pMODocumentItemVersion {
	p.pMODocumentItemVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMODocumentItemVersion) As(alias string) *pMODocumentItemVersion {
	p.pMODocumentItemVersionDo.DO = *(p.pMODocumentItemVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMODocumentItemVersion) updateTableName(table string) *pMODocumentItemVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.TabKey = field.NewString(table, "tab_key")
	p.GroupID = field.NewString(table, "group_id")
	p.Name = field.NewString(table, "name")
	p.SharepointURL = field.NewString(table, "sharepoint_url")
	p.Date = field.NewTime(table, "date")
	p.Type = field.NewString(table, "type")
	p.FileID = field.NewString(table, "file_id")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "document_item_id")

	p.fillFieldMap()

	return p
}

func (p *pMODocumentItemVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMODocumentItemVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 21)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["group_id"] = p.GroupID
	p.fieldMap["name"] = p.Name
	p.fieldMap["sharepoint_url"] = p.SharepointURL
	p.fieldMap["date"] = p.Date
	p.fieldMap["type"] = p.Type
	p.fieldMap["file_id"] = p.FileID
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["document_item_id"] = p.OriginalID

}

func (p pMODocumentItemVersion) clone(db *gorm.DB) pMODocumentItemVersion {
	p.pMODocumentItemVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.Group.db = db.Session(&gorm.Session{Initialized: true})
	p.Group.db.Statement.ConnPool = db.Statement.ConnPool
	p.File.db = db.Session(&gorm.Session{Initialized: true})
	p.File.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMODocumentItemVersion) replaceDB(db *gorm.DB) pMODocumentItemVersion {
	p.pMODocumentItemVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	p.Group.db = db.Session(&gorm.Session{})
	p.File.db = db.Session(&gorm.Session{})
	return p
}

type pMODocumentItemVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMODocumentItemVersionHasOneProject) Where(conds ...field.Expr) *pMODocumentItemVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemVersionHasOneProject) WithContext(ctx context.Context) *pMODocumentItemVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemVersionHasOneProject) Session(session *gorm.Session) *pMODocumentItemVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemVersionHasOneProject) Model(m *models.PMODocumentItemVersion) *pMODocumentItemVersionHasOneProjectTx {
	return &pMODocumentItemVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemVersionHasOneProject) Unscoped() *pMODocumentItemVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMODocumentItemVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemVersionHasOneProjectTx) Unscoped() *pMODocumentItemVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMODocumentItemVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMODocumentItemVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMODocumentItemVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemVersionBelongsToCreatedBy) Model(m *models.PMODocumentItemVersion) *pMODocumentItemVersionBelongsToCreatedByTx {
	return &pMODocumentItemVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemVersionBelongsToCreatedBy) Unscoped() *pMODocumentItemVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMODocumentItemVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemVersionBelongsToCreatedByTx) Unscoped() *pMODocumentItemVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMODocumentItemVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMODocumentItemVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMODocumentItemVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemVersionBelongsToUpdatedBy) Model(m *models.PMODocumentItemVersion) *pMODocumentItemVersionBelongsToUpdatedByTx {
	return &pMODocumentItemVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemVersionBelongsToUpdatedBy) Unscoped() *pMODocumentItemVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemVersionBelongsToUpdatedByTx) Unscoped() *pMODocumentItemVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToGroup struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemVersionBelongsToGroup) Where(conds ...field.Expr) *pMODocumentItemVersionBelongsToGroup {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemVersionBelongsToGroup) WithContext(ctx context.Context) *pMODocumentItemVersionBelongsToGroup {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemVersionBelongsToGroup) Session(session *gorm.Session) *pMODocumentItemVersionBelongsToGroup {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemVersionBelongsToGroup) Model(m *models.PMODocumentItemVersion) *pMODocumentItemVersionBelongsToGroupTx {
	return &pMODocumentItemVersionBelongsToGroupTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemVersionBelongsToGroup) Unscoped() *pMODocumentItemVersionBelongsToGroup {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToGroupTx struct{ tx *gorm.Association }

func (a pMODocumentItemVersionBelongsToGroupTx) Find() (result *models.PMODocumentGroup, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemVersionBelongsToGroupTx) Append(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemVersionBelongsToGroupTx) Replace(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemVersionBelongsToGroupTx) Delete(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemVersionBelongsToGroupTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemVersionBelongsToGroupTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemVersionBelongsToGroupTx) Unscoped() *pMODocumentItemVersionBelongsToGroupTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToFile struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemVersionBelongsToFile) Where(conds ...field.Expr) *pMODocumentItemVersionBelongsToFile {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemVersionBelongsToFile) WithContext(ctx context.Context) *pMODocumentItemVersionBelongsToFile {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemVersionBelongsToFile) Session(session *gorm.Session) *pMODocumentItemVersionBelongsToFile {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemVersionBelongsToFile) Model(m *models.PMODocumentItemVersion) *pMODocumentItemVersionBelongsToFileTx {
	return &pMODocumentItemVersionBelongsToFileTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemVersionBelongsToFile) Unscoped() *pMODocumentItemVersionBelongsToFile {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemVersionBelongsToFileTx struct{ tx *gorm.Association }

func (a pMODocumentItemVersionBelongsToFileTx) Find() (result *models.File, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemVersionBelongsToFileTx) Append(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemVersionBelongsToFileTx) Replace(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemVersionBelongsToFileTx) Delete(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemVersionBelongsToFileTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemVersionBelongsToFileTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemVersionBelongsToFileTx) Unscoped() *pMODocumentItemVersionBelongsToFileTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemVersionDo struct{ gen.DO }

type IPMODocumentItemVersionDo interface {
	gen.SubQuery
	Debug() IPMODocumentItemVersionDo
	WithContext(ctx context.Context) IPMODocumentItemVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMODocumentItemVersionDo
	WriteDB() IPMODocumentItemVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMODocumentItemVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMODocumentItemVersionDo
	Not(conds ...gen.Condition) IPMODocumentItemVersionDo
	Or(conds ...gen.Condition) IPMODocumentItemVersionDo
	Select(conds ...field.Expr) IPMODocumentItemVersionDo
	Where(conds ...gen.Condition) IPMODocumentItemVersionDo
	Order(conds ...field.Expr) IPMODocumentItemVersionDo
	Distinct(cols ...field.Expr) IPMODocumentItemVersionDo
	Omit(cols ...field.Expr) IPMODocumentItemVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo
	Group(cols ...field.Expr) IPMODocumentItemVersionDo
	Having(conds ...gen.Condition) IPMODocumentItemVersionDo
	Limit(limit int) IPMODocumentItemVersionDo
	Offset(offset int) IPMODocumentItemVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentItemVersionDo
	Unscoped() IPMODocumentItemVersionDo
	Create(values ...*models.PMODocumentItemVersion) error
	CreateInBatches(values []*models.PMODocumentItemVersion, batchSize int) error
	Save(values ...*models.PMODocumentItemVersion) error
	First() (*models.PMODocumentItemVersion, error)
	Take() (*models.PMODocumentItemVersion, error)
	Last() (*models.PMODocumentItemVersion, error)
	Find() ([]*models.PMODocumentItemVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentItemVersion, err error)
	FindInBatches(result *[]*models.PMODocumentItemVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMODocumentItemVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMODocumentItemVersionDo
	Assign(attrs ...field.AssignExpr) IPMODocumentItemVersionDo
	Joins(fields ...field.RelationField) IPMODocumentItemVersionDo
	Preload(fields ...field.RelationField) IPMODocumentItemVersionDo
	FirstOrInit() (*models.PMODocumentItemVersion, error)
	FirstOrCreate() (*models.PMODocumentItemVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMODocumentItemVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMODocumentItemVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMODocumentItemVersionDo) Debug() IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMODocumentItemVersionDo) WithContext(ctx context.Context) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMODocumentItemVersionDo) ReadDB() IPMODocumentItemVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMODocumentItemVersionDo) WriteDB() IPMODocumentItemVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMODocumentItemVersionDo) Session(config *gorm.Session) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMODocumentItemVersionDo) Clauses(conds ...clause.Expression) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMODocumentItemVersionDo) Returning(value interface{}, columns ...string) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMODocumentItemVersionDo) Not(conds ...gen.Condition) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMODocumentItemVersionDo) Or(conds ...gen.Condition) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMODocumentItemVersionDo) Select(conds ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMODocumentItemVersionDo) Where(conds ...gen.Condition) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMODocumentItemVersionDo) Order(conds ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMODocumentItemVersionDo) Distinct(cols ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMODocumentItemVersionDo) Omit(cols ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMODocumentItemVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMODocumentItemVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMODocumentItemVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMODocumentItemVersionDo) Group(cols ...field.Expr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMODocumentItemVersionDo) Having(conds ...gen.Condition) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMODocumentItemVersionDo) Limit(limit int) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMODocumentItemVersionDo) Offset(offset int) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMODocumentItemVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMODocumentItemVersionDo) Unscoped() IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMODocumentItemVersionDo) Create(values ...*models.PMODocumentItemVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMODocumentItemVersionDo) CreateInBatches(values []*models.PMODocumentItemVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMODocumentItemVersionDo) Save(values ...*models.PMODocumentItemVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMODocumentItemVersionDo) First() (*models.PMODocumentItemVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItemVersion), nil
	}
}

func (p pMODocumentItemVersionDo) Take() (*models.PMODocumentItemVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItemVersion), nil
	}
}

func (p pMODocumentItemVersionDo) Last() (*models.PMODocumentItemVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItemVersion), nil
	}
}

func (p pMODocumentItemVersionDo) Find() ([]*models.PMODocumentItemVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMODocumentItemVersion), err
}

func (p pMODocumentItemVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentItemVersion, err error) {
	buf := make([]*models.PMODocumentItemVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMODocumentItemVersionDo) FindInBatches(result *[]*models.PMODocumentItemVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMODocumentItemVersionDo) Attrs(attrs ...field.AssignExpr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMODocumentItemVersionDo) Assign(attrs ...field.AssignExpr) IPMODocumentItemVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMODocumentItemVersionDo) Joins(fields ...field.RelationField) IPMODocumentItemVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMODocumentItemVersionDo) Preload(fields ...field.RelationField) IPMODocumentItemVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMODocumentItemVersionDo) FirstOrInit() (*models.PMODocumentItemVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItemVersion), nil
	}
}

func (p pMODocumentItemVersionDo) FirstOrCreate() (*models.PMODocumentItemVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItemVersion), nil
	}
}

func (p pMODocumentItemVersionDo) FindByPage(offset int, limit int) (result []*models.PMODocumentItemVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMODocumentItemVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMODocumentItemVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMODocumentItemVersionDo) Delete(models ...*models.PMODocumentItemVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMODocumentItemVersionDo) withDO(do gen.Dao) *pMODocumentItemVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
