// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOVendorItem(db *gorm.DB, opts ...gen.DOOption) pMOVendorItem {
	_pMOVendorItem := pMOVendorItem{}

	_pMOVendorItem.pMOVendorItemDo.UseDB(db, opts...)
	_pMOVendorItem.pMOVendorItemDo.UseModel(&models.PMOVendorItem{})

	tableName := _pMOVendorItem.pMOVendorItemDo.TableName()
	_pMOVendorItem.ALL = field.NewAsterisk(tableName)
	_pMOVendorItem.ID = field.NewString(tableName, "id")
	_pMOVendorItem.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOVendorItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOVendorItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOVendorItem.ProjectID = field.NewString(tableName, "project_id")
	_pMOVendorItem.VendorName = field.NewString(tableName, "vendor_name")
	_pMOVendorItem.ItemName = field.NewString(tableName, "item_name")
	_pMOVendorItem.ItemDetail = field.NewString(tableName, "item_detail")
	_pMOVendorItem.DeliverDurationDay = field.NewInt64(tableName, "deliver_duration_day")
	_pMOVendorItem.IsTor = field.NewBool(tableName, "is_tor")
	_pMOVendorItem.IsImplementation = field.NewBool(tableName, "is_implementation")
	_pMOVendorItem.IsTraining = field.NewBool(tableName, "is_training")
	_pMOVendorItem.IsUserManual = field.NewBool(tableName, "is_user_manual")
	_pMOVendorItem.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOVendorItem.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOVendorItem.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOVendorItem.Project = pMOVendorItemHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOVendorItem.CreatedBy = pMOVendorItemBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOVendorItem.UpdatedBy = pMOVendorItemBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOVendorItem.fillFieldMap()

	return _pMOVendorItem
}

type pMOVendorItem struct {
	pMOVendorItemDo

	ALL                field.Asterisk
	ID                 field.String
	CreatedAt          field.Time
	UpdatedAt          field.Time
	DeletedAt          field.Field
	ProjectID          field.String
	VendorName         field.String
	ItemName           field.String
	ItemDetail         field.String
	DeliverDurationDay field.Int64
	IsTor              field.Bool
	IsImplementation   field.Bool
	IsTraining         field.Bool
	IsUserManual       field.Bool
	CreatedByID        field.String
	UpdatedByID        field.String
	DeletedByID        field.String
	Project            pMOVendorItemHasOneProject

	CreatedBy pMOVendorItemBelongsToCreatedBy

	UpdatedBy pMOVendorItemBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOVendorItem) Table(newTableName string) *pMOVendorItem {
	p.pMOVendorItemDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOVendorItem) As(alias string) *pMOVendorItem {
	p.pMOVendorItemDo.DO = *(p.pMOVendorItemDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOVendorItem) updateTableName(table string) *pMOVendorItem {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.VendorName = field.NewString(table, "vendor_name")
	p.ItemName = field.NewString(table, "item_name")
	p.ItemDetail = field.NewString(table, "item_detail")
	p.DeliverDurationDay = field.NewInt64(table, "deliver_duration_day")
	p.IsTor = field.NewBool(table, "is_tor")
	p.IsImplementation = field.NewBool(table, "is_implementation")
	p.IsTraining = field.NewBool(table, "is_training")
	p.IsUserManual = field.NewBool(table, "is_user_manual")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOVendorItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOVendorItem) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 19)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["vendor_name"] = p.VendorName
	p.fieldMap["item_name"] = p.ItemName
	p.fieldMap["item_detail"] = p.ItemDetail
	p.fieldMap["deliver_duration_day"] = p.DeliverDurationDay
	p.fieldMap["is_tor"] = p.IsTor
	p.fieldMap["is_implementation"] = p.IsImplementation
	p.fieldMap["is_training"] = p.IsTraining
	p.fieldMap["is_user_manual"] = p.IsUserManual
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOVendorItem) clone(db *gorm.DB) pMOVendorItem {
	p.pMOVendorItemDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOVendorItem) replaceDB(db *gorm.DB) pMOVendorItem {
	p.pMOVendorItemDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOVendorItemHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOVendorItemHasOneProject) Where(conds ...field.Expr) *pMOVendorItemHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOVendorItemHasOneProject) WithContext(ctx context.Context) *pMOVendorItemHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOVendorItemHasOneProject) Session(session *gorm.Session) *pMOVendorItemHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOVendorItemHasOneProject) Model(m *models.PMOVendorItem) *pMOVendorItemHasOneProjectTx {
	return &pMOVendorItemHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOVendorItemHasOneProject) Unscoped() *pMOVendorItemHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOVendorItemHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOVendorItemHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOVendorItemHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOVendorItemHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOVendorItemHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOVendorItemHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOVendorItemHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOVendorItemHasOneProjectTx) Unscoped() *pMOVendorItemHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOVendorItemBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOVendorItemBelongsToCreatedBy) Where(conds ...field.Expr) *pMOVendorItemBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOVendorItemBelongsToCreatedBy) WithContext(ctx context.Context) *pMOVendorItemBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOVendorItemBelongsToCreatedBy) Session(session *gorm.Session) *pMOVendorItemBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOVendorItemBelongsToCreatedBy) Model(m *models.PMOVendorItem) *pMOVendorItemBelongsToCreatedByTx {
	return &pMOVendorItemBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOVendorItemBelongsToCreatedBy) Unscoped() *pMOVendorItemBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOVendorItemBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOVendorItemBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOVendorItemBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOVendorItemBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOVendorItemBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOVendorItemBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOVendorItemBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOVendorItemBelongsToCreatedByTx) Unscoped() *pMOVendorItemBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOVendorItemBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOVendorItemBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOVendorItemBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOVendorItemBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOVendorItemBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOVendorItemBelongsToUpdatedBy) Session(session *gorm.Session) *pMOVendorItemBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOVendorItemBelongsToUpdatedBy) Model(m *models.PMOVendorItem) *pMOVendorItemBelongsToUpdatedByTx {
	return &pMOVendorItemBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOVendorItemBelongsToUpdatedBy) Unscoped() *pMOVendorItemBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOVendorItemBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOVendorItemBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOVendorItemBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOVendorItemBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOVendorItemBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOVendorItemBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOVendorItemBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOVendorItemBelongsToUpdatedByTx) Unscoped() *pMOVendorItemBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOVendorItemDo struct{ gen.DO }

type IPMOVendorItemDo interface {
	gen.SubQuery
	Debug() IPMOVendorItemDo
	WithContext(ctx context.Context) IPMOVendorItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOVendorItemDo
	WriteDB() IPMOVendorItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOVendorItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOVendorItemDo
	Not(conds ...gen.Condition) IPMOVendorItemDo
	Or(conds ...gen.Condition) IPMOVendorItemDo
	Select(conds ...field.Expr) IPMOVendorItemDo
	Where(conds ...gen.Condition) IPMOVendorItemDo
	Order(conds ...field.Expr) IPMOVendorItemDo
	Distinct(cols ...field.Expr) IPMOVendorItemDo
	Omit(cols ...field.Expr) IPMOVendorItemDo
	Join(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo
	Group(cols ...field.Expr) IPMOVendorItemDo
	Having(conds ...gen.Condition) IPMOVendorItemDo
	Limit(limit int) IPMOVendorItemDo
	Offset(offset int) IPMOVendorItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOVendorItemDo
	Unscoped() IPMOVendorItemDo
	Create(values ...*models.PMOVendorItem) error
	CreateInBatches(values []*models.PMOVendorItem, batchSize int) error
	Save(values ...*models.PMOVendorItem) error
	First() (*models.PMOVendorItem, error)
	Take() (*models.PMOVendorItem, error)
	Last() (*models.PMOVendorItem, error)
	Find() ([]*models.PMOVendorItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOVendorItem, err error)
	FindInBatches(result *[]*models.PMOVendorItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOVendorItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOVendorItemDo
	Assign(attrs ...field.AssignExpr) IPMOVendorItemDo
	Joins(fields ...field.RelationField) IPMOVendorItemDo
	Preload(fields ...field.RelationField) IPMOVendorItemDo
	FirstOrInit() (*models.PMOVendorItem, error)
	FirstOrCreate() (*models.PMOVendorItem, error)
	FindByPage(offset int, limit int) (result []*models.PMOVendorItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOVendorItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOVendorItemDo) Debug() IPMOVendorItemDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOVendorItemDo) WithContext(ctx context.Context) IPMOVendorItemDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOVendorItemDo) ReadDB() IPMOVendorItemDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOVendorItemDo) WriteDB() IPMOVendorItemDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOVendorItemDo) Session(config *gorm.Session) IPMOVendorItemDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOVendorItemDo) Clauses(conds ...clause.Expression) IPMOVendorItemDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOVendorItemDo) Returning(value interface{}, columns ...string) IPMOVendorItemDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOVendorItemDo) Not(conds ...gen.Condition) IPMOVendorItemDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOVendorItemDo) Or(conds ...gen.Condition) IPMOVendorItemDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOVendorItemDo) Select(conds ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOVendorItemDo) Where(conds ...gen.Condition) IPMOVendorItemDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOVendorItemDo) Order(conds ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOVendorItemDo) Distinct(cols ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOVendorItemDo) Omit(cols ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOVendorItemDo) Join(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOVendorItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOVendorItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOVendorItemDo) Group(cols ...field.Expr) IPMOVendorItemDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOVendorItemDo) Having(conds ...gen.Condition) IPMOVendorItemDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOVendorItemDo) Limit(limit int) IPMOVendorItemDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOVendorItemDo) Offset(offset int) IPMOVendorItemDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOVendorItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOVendorItemDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOVendorItemDo) Unscoped() IPMOVendorItemDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOVendorItemDo) Create(values ...*models.PMOVendorItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOVendorItemDo) CreateInBatches(values []*models.PMOVendorItem, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOVendorItemDo) Save(values ...*models.PMOVendorItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOVendorItemDo) First() (*models.PMOVendorItem, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOVendorItem), nil
	}
}

func (p pMOVendorItemDo) Take() (*models.PMOVendorItem, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOVendorItem), nil
	}
}

func (p pMOVendorItemDo) Last() (*models.PMOVendorItem, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOVendorItem), nil
	}
}

func (p pMOVendorItemDo) Find() ([]*models.PMOVendorItem, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOVendorItem), err
}

func (p pMOVendorItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOVendorItem, err error) {
	buf := make([]*models.PMOVendorItem, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOVendorItemDo) FindInBatches(result *[]*models.PMOVendorItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOVendorItemDo) Attrs(attrs ...field.AssignExpr) IPMOVendorItemDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOVendorItemDo) Assign(attrs ...field.AssignExpr) IPMOVendorItemDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOVendorItemDo) Joins(fields ...field.RelationField) IPMOVendorItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOVendorItemDo) Preload(fields ...field.RelationField) IPMOVendorItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOVendorItemDo) FirstOrInit() (*models.PMOVendorItem, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOVendorItem), nil
	}
}

func (p pMOVendorItemDo) FirstOrCreate() (*models.PMOVendorItem, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOVendorItem), nil
	}
}

func (p pMOVendorItemDo) FindByPage(offset int, limit int) (result []*models.PMOVendorItem, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOVendorItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOVendorItemDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOVendorItemDo) Delete(models ...*models.PMOVendorItem) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOVendorItemDo) withDO(do gen.Dao) *pMOVendorItemDo {
	p.DO = *do.(*gen.DO)
	return p
}
