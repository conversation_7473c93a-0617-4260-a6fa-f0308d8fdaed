// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOLGInfo(db *gorm.DB, opts ...gen.DOOption) pMOLGInfo {
	_pMOLGInfo := pMOLGInfo{}

	_pMOLGInfo.pMOLGInfoDo.UseDB(db, opts...)
	_pMOLGInfo.pMOLGInfoDo.UseModel(&models.PMOLGInfo{})

	tableName := _pMOLGInfo.pMOLGInfoDo.TableName()
	_pMOLGInfo.ALL = field.NewAsterisk(tableName)
	_pMOLGInfo.ID = field.NewString(tableName, "id")
	_pMOLGInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOLGInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOLGInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOLGInfo.ProjectID = field.NewString(tableName, "project_id")
	_pMOLGInfo.Value = field.NewFloat64(tableName, "value")
	_pMOLGInfo.StartDate = field.NewTime(tableName, "start_date")
	_pMOLGInfo.EndDate = field.NewTime(tableName, "end_date")
	_pMOLGInfo.Fee = field.NewFloat64(tableName, "fee")
	_pMOLGInfo.Interest = field.NewFloat64(tableName, "interest")
	_pMOLGInfo.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOLGInfo.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOLGInfo.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOLGInfo.Project = pMOLGInfoHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOLGInfo.CreatedBy = pMOLGInfoBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOLGInfo.UpdatedBy = pMOLGInfoBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOLGInfo.fillFieldMap()

	return _pMOLGInfo
}

type pMOLGInfo struct {
	pMOLGInfoDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	ProjectID   field.String
	Value       field.Float64
	StartDate   field.Time
	EndDate     field.Time
	Fee         field.Float64
	Interest    field.Float64
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	Project     pMOLGInfoHasOneProject

	CreatedBy pMOLGInfoBelongsToCreatedBy

	UpdatedBy pMOLGInfoBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOLGInfo) Table(newTableName string) *pMOLGInfo {
	p.pMOLGInfoDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOLGInfo) As(alias string) *pMOLGInfo {
	p.pMOLGInfoDo.DO = *(p.pMOLGInfoDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOLGInfo) updateTableName(table string) *pMOLGInfo {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.Value = field.NewFloat64(table, "value")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.Fee = field.NewFloat64(table, "fee")
	p.Interest = field.NewFloat64(table, "interest")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOLGInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOLGInfo) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["value"] = p.Value
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["fee"] = p.Fee
	p.fieldMap["interest"] = p.Interest
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOLGInfo) clone(db *gorm.DB) pMOLGInfo {
	p.pMOLGInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOLGInfo) replaceDB(db *gorm.DB) pMOLGInfo {
	p.pMOLGInfoDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOLGInfoHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOLGInfoHasOneProject) Where(conds ...field.Expr) *pMOLGInfoHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoHasOneProject) WithContext(ctx context.Context) *pMOLGInfoHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoHasOneProject) Session(session *gorm.Session) *pMOLGInfoHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoHasOneProject) Model(m *models.PMOLGInfo) *pMOLGInfoHasOneProjectTx {
	return &pMOLGInfoHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoHasOneProject) Unscoped() *pMOLGInfoHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOLGInfoHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoHasOneProjectTx) Unscoped() *pMOLGInfoHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOLGInfoBelongsToCreatedBy) Where(conds ...field.Expr) *pMOLGInfoBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoBelongsToCreatedBy) WithContext(ctx context.Context) *pMOLGInfoBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoBelongsToCreatedBy) Session(session *gorm.Session) *pMOLGInfoBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoBelongsToCreatedBy) Model(m *models.PMOLGInfo) *pMOLGInfoBelongsToCreatedByTx {
	return &pMOLGInfoBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoBelongsToCreatedBy) Unscoped() *pMOLGInfoBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOLGInfoBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoBelongsToCreatedByTx) Unscoped() *pMOLGInfoBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOLGInfoBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOLGInfoBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOLGInfoBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoBelongsToUpdatedBy) Session(session *gorm.Session) *pMOLGInfoBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoBelongsToUpdatedBy) Model(m *models.PMOLGInfo) *pMOLGInfoBelongsToUpdatedByTx {
	return &pMOLGInfoBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoBelongsToUpdatedBy) Unscoped() *pMOLGInfoBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOLGInfoBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoBelongsToUpdatedByTx) Unscoped() *pMOLGInfoBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoDo struct{ gen.DO }

type IPMOLGInfoDo interface {
	gen.SubQuery
	Debug() IPMOLGInfoDo
	WithContext(ctx context.Context) IPMOLGInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOLGInfoDo
	WriteDB() IPMOLGInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOLGInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOLGInfoDo
	Not(conds ...gen.Condition) IPMOLGInfoDo
	Or(conds ...gen.Condition) IPMOLGInfoDo
	Select(conds ...field.Expr) IPMOLGInfoDo
	Where(conds ...gen.Condition) IPMOLGInfoDo
	Order(conds ...field.Expr) IPMOLGInfoDo
	Distinct(cols ...field.Expr) IPMOLGInfoDo
	Omit(cols ...field.Expr) IPMOLGInfoDo
	Join(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo
	Group(cols ...field.Expr) IPMOLGInfoDo
	Having(conds ...gen.Condition) IPMOLGInfoDo
	Limit(limit int) IPMOLGInfoDo
	Offset(offset int) IPMOLGInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOLGInfoDo
	Unscoped() IPMOLGInfoDo
	Create(values ...*models.PMOLGInfo) error
	CreateInBatches(values []*models.PMOLGInfo, batchSize int) error
	Save(values ...*models.PMOLGInfo) error
	First() (*models.PMOLGInfo, error)
	Take() (*models.PMOLGInfo, error)
	Last() (*models.PMOLGInfo, error)
	Find() ([]*models.PMOLGInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOLGInfo, err error)
	FindInBatches(result *[]*models.PMOLGInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOLGInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOLGInfoDo
	Assign(attrs ...field.AssignExpr) IPMOLGInfoDo
	Joins(fields ...field.RelationField) IPMOLGInfoDo
	Preload(fields ...field.RelationField) IPMOLGInfoDo
	FirstOrInit() (*models.PMOLGInfo, error)
	FirstOrCreate() (*models.PMOLGInfo, error)
	FindByPage(offset int, limit int) (result []*models.PMOLGInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOLGInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOLGInfoDo) Debug() IPMOLGInfoDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOLGInfoDo) WithContext(ctx context.Context) IPMOLGInfoDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOLGInfoDo) ReadDB() IPMOLGInfoDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOLGInfoDo) WriteDB() IPMOLGInfoDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOLGInfoDo) Session(config *gorm.Session) IPMOLGInfoDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOLGInfoDo) Clauses(conds ...clause.Expression) IPMOLGInfoDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOLGInfoDo) Returning(value interface{}, columns ...string) IPMOLGInfoDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOLGInfoDo) Not(conds ...gen.Condition) IPMOLGInfoDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOLGInfoDo) Or(conds ...gen.Condition) IPMOLGInfoDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOLGInfoDo) Select(conds ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOLGInfoDo) Where(conds ...gen.Condition) IPMOLGInfoDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOLGInfoDo) Order(conds ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOLGInfoDo) Distinct(cols ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOLGInfoDo) Omit(cols ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOLGInfoDo) Join(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOLGInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOLGInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOLGInfoDo) Group(cols ...field.Expr) IPMOLGInfoDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOLGInfoDo) Having(conds ...gen.Condition) IPMOLGInfoDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOLGInfoDo) Limit(limit int) IPMOLGInfoDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOLGInfoDo) Offset(offset int) IPMOLGInfoDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOLGInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOLGInfoDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOLGInfoDo) Unscoped() IPMOLGInfoDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOLGInfoDo) Create(values ...*models.PMOLGInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOLGInfoDo) CreateInBatches(values []*models.PMOLGInfo, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOLGInfoDo) Save(values ...*models.PMOLGInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOLGInfoDo) First() (*models.PMOLGInfo, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfo), nil
	}
}

func (p pMOLGInfoDo) Take() (*models.PMOLGInfo, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfo), nil
	}
}

func (p pMOLGInfoDo) Last() (*models.PMOLGInfo, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfo), nil
	}
}

func (p pMOLGInfoDo) Find() ([]*models.PMOLGInfo, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOLGInfo), err
}

func (p pMOLGInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOLGInfo, err error) {
	buf := make([]*models.PMOLGInfo, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOLGInfoDo) FindInBatches(result *[]*models.PMOLGInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOLGInfoDo) Attrs(attrs ...field.AssignExpr) IPMOLGInfoDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOLGInfoDo) Assign(attrs ...field.AssignExpr) IPMOLGInfoDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOLGInfoDo) Joins(fields ...field.RelationField) IPMOLGInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOLGInfoDo) Preload(fields ...field.RelationField) IPMOLGInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOLGInfoDo) FirstOrInit() (*models.PMOLGInfo, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfo), nil
	}
}

func (p pMOLGInfoDo) FirstOrCreate() (*models.PMOLGInfo, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfo), nil
	}
}

func (p pMOLGInfoDo) FindByPage(offset int, limit int) (result []*models.PMOLGInfo, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOLGInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOLGInfoDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOLGInfoDo) Delete(models ...*models.PMOLGInfo) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOLGInfoDo) withDO(do gen.Dao) *pMOLGInfoDo {
	p.DO = *do.(*gen.DO)
	return p
}
