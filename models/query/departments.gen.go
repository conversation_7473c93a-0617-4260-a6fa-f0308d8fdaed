// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newDepartment(db *gorm.DB, opts ...gen.DOOption) department {
	_department := department{}

	_department.departmentDo.UseDB(db, opts...)
	_department.departmentDo.UseModel(&models.Department{})

	tableName := _department.departmentDo.TableName()
	_department.ALL = field.NewAsterisk(tableName)
	_department.ID = field.NewString(tableName, "id")
	_department.CreatedAt = field.NewTime(tableName, "created_at")
	_department.UpdatedAt = field.NewTime(tableName, "updated_at")
	_department.DeletedAt = field.NewField(tableName, "deleted_at")
	_department.MinistryID = field.NewString(tableName, "ministry_id")
	_department.NameTh = field.NewString(tableName, "name_th")
	_department.NameEn = field.NewString(tableName, "name_en")
	_department.CreatedByID = field.NewString(tableName, "created_by_id")
	_department.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_department.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_department.Ministry = departmentBelongsToMinistry{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Ministry", "models.Ministry"),
		Departments: struct {
			field.RelationField
			Ministry struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Ministry.Departments", "models.Department"),
			Ministry: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Ministry.Departments.Ministry", "models.Ministry"),
			},
		},
	}

	_department.fillFieldMap()

	return _department
}

type department struct {
	departmentDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	MinistryID  field.String
	NameTh      field.String
	NameEn      field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	Ministry    departmentBelongsToMinistry

	fieldMap map[string]field.Expr
}

func (d department) Table(newTableName string) *department {
	d.departmentDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d department) As(alias string) *department {
	d.departmentDo.DO = *(d.departmentDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *department) updateTableName(table string) *department {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewString(table, "id")
	d.CreatedAt = field.NewTime(table, "created_at")
	d.UpdatedAt = field.NewTime(table, "updated_at")
	d.DeletedAt = field.NewField(table, "deleted_at")
	d.MinistryID = field.NewString(table, "ministry_id")
	d.NameTh = field.NewString(table, "name_th")
	d.NameEn = field.NewString(table, "name_en")
	d.CreatedByID = field.NewString(table, "created_by_id")
	d.UpdatedByID = field.NewString(table, "updated_by_id")
	d.DeletedByID = field.NewString(table, "deleted_by_id")

	d.fillFieldMap()

	return d
}

func (d *department) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *department) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 11)
	d.fieldMap["id"] = d.ID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["ministry_id"] = d.MinistryID
	d.fieldMap["name_th"] = d.NameTh
	d.fieldMap["name_en"] = d.NameEn
	d.fieldMap["created_by_id"] = d.CreatedByID
	d.fieldMap["updated_by_id"] = d.UpdatedByID
	d.fieldMap["deleted_by_id"] = d.DeletedByID

}

func (d department) clone(db *gorm.DB) department {
	d.departmentDo.ReplaceConnPool(db.Statement.ConnPool)
	d.Ministry.db = db.Session(&gorm.Session{Initialized: true})
	d.Ministry.db.Statement.ConnPool = db.Statement.ConnPool
	return d
}

func (d department) replaceDB(db *gorm.DB) department {
	d.departmentDo.ReplaceDB(db)
	d.Ministry.db = db.Session(&gorm.Session{})
	return d
}

type departmentBelongsToMinistry struct {
	db *gorm.DB

	field.RelationField

	Departments struct {
		field.RelationField
		Ministry struct {
			field.RelationField
		}
	}
}

func (a departmentBelongsToMinistry) Where(conds ...field.Expr) *departmentBelongsToMinistry {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a departmentBelongsToMinistry) WithContext(ctx context.Context) *departmentBelongsToMinistry {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a departmentBelongsToMinistry) Session(session *gorm.Session) *departmentBelongsToMinistry {
	a.db = a.db.Session(session)
	return &a
}

func (a departmentBelongsToMinistry) Model(m *models.Department) *departmentBelongsToMinistryTx {
	return &departmentBelongsToMinistryTx{a.db.Model(m).Association(a.Name())}
}

func (a departmentBelongsToMinistry) Unscoped() *departmentBelongsToMinistry {
	a.db = a.db.Unscoped()
	return &a
}

type departmentBelongsToMinistryTx struct{ tx *gorm.Association }

func (a departmentBelongsToMinistryTx) Find() (result *models.Ministry, err error) {
	return result, a.tx.Find(&result)
}

func (a departmentBelongsToMinistryTx) Append(values ...*models.Ministry) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a departmentBelongsToMinistryTx) Replace(values ...*models.Ministry) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a departmentBelongsToMinistryTx) Delete(values ...*models.Ministry) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a departmentBelongsToMinistryTx) Clear() error {
	return a.tx.Clear()
}

func (a departmentBelongsToMinistryTx) Count() int64 {
	return a.tx.Count()
}

func (a departmentBelongsToMinistryTx) Unscoped() *departmentBelongsToMinistryTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type departmentDo struct{ gen.DO }

type IDepartmentDo interface {
	gen.SubQuery
	Debug() IDepartmentDo
	WithContext(ctx context.Context) IDepartmentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDepartmentDo
	WriteDB() IDepartmentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDepartmentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDepartmentDo
	Not(conds ...gen.Condition) IDepartmentDo
	Or(conds ...gen.Condition) IDepartmentDo
	Select(conds ...field.Expr) IDepartmentDo
	Where(conds ...gen.Condition) IDepartmentDo
	Order(conds ...field.Expr) IDepartmentDo
	Distinct(cols ...field.Expr) IDepartmentDo
	Omit(cols ...field.Expr) IDepartmentDo
	Join(table schema.Tabler, on ...field.Expr) IDepartmentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDepartmentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDepartmentDo
	Group(cols ...field.Expr) IDepartmentDo
	Having(conds ...gen.Condition) IDepartmentDo
	Limit(limit int) IDepartmentDo
	Offset(offset int) IDepartmentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDepartmentDo
	Unscoped() IDepartmentDo
	Create(values ...*models.Department) error
	CreateInBatches(values []*models.Department, batchSize int) error
	Save(values ...*models.Department) error
	First() (*models.Department, error)
	Take() (*models.Department, error)
	Last() (*models.Department, error)
	Find() ([]*models.Department, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Department, err error)
	FindInBatches(result *[]*models.Department, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Department) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDepartmentDo
	Assign(attrs ...field.AssignExpr) IDepartmentDo
	Joins(fields ...field.RelationField) IDepartmentDo
	Preload(fields ...field.RelationField) IDepartmentDo
	FirstOrInit() (*models.Department, error)
	FirstOrCreate() (*models.Department, error)
	FindByPage(offset int, limit int) (result []*models.Department, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDepartmentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d departmentDo) Debug() IDepartmentDo {
	return d.withDO(d.DO.Debug())
}

func (d departmentDo) WithContext(ctx context.Context) IDepartmentDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d departmentDo) ReadDB() IDepartmentDo {
	return d.Clauses(dbresolver.Read)
}

func (d departmentDo) WriteDB() IDepartmentDo {
	return d.Clauses(dbresolver.Write)
}

func (d departmentDo) Session(config *gorm.Session) IDepartmentDo {
	return d.withDO(d.DO.Session(config))
}

func (d departmentDo) Clauses(conds ...clause.Expression) IDepartmentDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d departmentDo) Returning(value interface{}, columns ...string) IDepartmentDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d departmentDo) Not(conds ...gen.Condition) IDepartmentDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d departmentDo) Or(conds ...gen.Condition) IDepartmentDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d departmentDo) Select(conds ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d departmentDo) Where(conds ...gen.Condition) IDepartmentDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d departmentDo) Order(conds ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d departmentDo) Distinct(cols ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d departmentDo) Omit(cols ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d departmentDo) Join(table schema.Tabler, on ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d departmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d departmentDo) RightJoin(table schema.Tabler, on ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d departmentDo) Group(cols ...field.Expr) IDepartmentDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d departmentDo) Having(conds ...gen.Condition) IDepartmentDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d departmentDo) Limit(limit int) IDepartmentDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d departmentDo) Offset(offset int) IDepartmentDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d departmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDepartmentDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d departmentDo) Unscoped() IDepartmentDo {
	return d.withDO(d.DO.Unscoped())
}

func (d departmentDo) Create(values ...*models.Department) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d departmentDo) CreateInBatches(values []*models.Department, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d departmentDo) Save(values ...*models.Department) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d departmentDo) First() (*models.Department, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Department), nil
	}
}

func (d departmentDo) Take() (*models.Department, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Department), nil
	}
}

func (d departmentDo) Last() (*models.Department, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Department), nil
	}
}

func (d departmentDo) Find() ([]*models.Department, error) {
	result, err := d.DO.Find()
	return result.([]*models.Department), err
}

func (d departmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Department, err error) {
	buf := make([]*models.Department, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d departmentDo) FindInBatches(result *[]*models.Department, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d departmentDo) Attrs(attrs ...field.AssignExpr) IDepartmentDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d departmentDo) Assign(attrs ...field.AssignExpr) IDepartmentDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d departmentDo) Joins(fields ...field.RelationField) IDepartmentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d departmentDo) Preload(fields ...field.RelationField) IDepartmentDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d departmentDo) FirstOrInit() (*models.Department, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Department), nil
	}
}

func (d departmentDo) FirstOrCreate() (*models.Department, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Department), nil
	}
}

func (d departmentDo) FindByPage(offset int, limit int) (result []*models.Department, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d departmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d departmentDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d departmentDo) Delete(models ...*models.Department) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *departmentDo) withDO(do gen.Dao) *departmentDo {
	d.DO = *do.(*gen.DO)
	return d
}
