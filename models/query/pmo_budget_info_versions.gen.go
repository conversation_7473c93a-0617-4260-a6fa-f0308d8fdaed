// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBudgetInfoVersion(db *gorm.DB, opts ...gen.DOOption) pMOBudgetInfoVersion {
	_pMOBudgetInfoVersion := pMOBudgetInfoVersion{}

	_pMOBudgetInfoVersion.pMOBudgetInfoVersionDo.UseDB(db, opts...)
	_pMOBudgetInfoVersion.pMOBudgetInfoVersionDo.UseModel(&models.PMOBudgetInfoVersion{})

	tableName := _pMOBudgetInfoVersion.pMOBudgetInfoVersionDo.TableName()
	_pMOBudgetInfoVersion.ALL = field.NewAsterisk(tableName)
	_pMOBudgetInfoVersion.ID = field.NewString(tableName, "id")
	_pMOBudgetInfoVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBudgetInfoVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBudgetInfoVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBudgetInfoVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOBudgetInfoVersion.FundType = field.NewString(tableName, "fund_type")
	_pMOBudgetInfoVersion.ProjectValue = field.NewFloat64(tableName, "project_value")
	_pMOBudgetInfoVersion.BidbondValue = field.NewFloat64(tableName, "bidbond_value")
	_pMOBudgetInfoVersion.Partner = field.NewString(tableName, "partner")
	_pMOBudgetInfoVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBudgetInfoVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBudgetInfoVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBudgetInfoVersion.OriginalID = field.NewString(tableName, "budget_info_id")
	_pMOBudgetInfoVersion.Project = pMOBudgetInfoVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBudgetInfoVersion.CreatedBy = pMOBudgetInfoVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBudgetInfoVersion.UpdatedBy = pMOBudgetInfoVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBudgetInfoVersion.fillFieldMap()

	return _pMOBudgetInfoVersion
}

type pMOBudgetInfoVersion struct {
	pMOBudgetInfoVersionDo

	ALL          field.Asterisk
	ID           field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ProjectID    field.String
	FundType     field.String
	ProjectValue field.Float64
	BidbondValue field.Float64
	Partner      field.String
	CreatedByID  field.String
	UpdatedByID  field.String
	DeletedByID  field.String
	OriginalID   field.String
	Project      pMOBudgetInfoVersionHasOneProject

	CreatedBy pMOBudgetInfoVersionBelongsToCreatedBy

	UpdatedBy pMOBudgetInfoVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBudgetInfoVersion) Table(newTableName string) *pMOBudgetInfoVersion {
	p.pMOBudgetInfoVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBudgetInfoVersion) As(alias string) *pMOBudgetInfoVersion {
	p.pMOBudgetInfoVersionDo.DO = *(p.pMOBudgetInfoVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBudgetInfoVersion) updateTableName(table string) *pMOBudgetInfoVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.FundType = field.NewString(table, "fund_type")
	p.ProjectValue = field.NewFloat64(table, "project_value")
	p.BidbondValue = field.NewFloat64(table, "bidbond_value")
	p.Partner = field.NewString(table, "partner")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "budget_info_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBudgetInfoVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBudgetInfoVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["fund_type"] = p.FundType
	p.fieldMap["project_value"] = p.ProjectValue
	p.fieldMap["bidbond_value"] = p.BidbondValue
	p.fieldMap["partner"] = p.Partner
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["budget_info_id"] = p.OriginalID

}

func (p pMOBudgetInfoVersion) clone(db *gorm.DB) pMOBudgetInfoVersion {
	p.pMOBudgetInfoVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBudgetInfoVersion) replaceDB(db *gorm.DB) pMOBudgetInfoVersion {
	p.pMOBudgetInfoVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBudgetInfoVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBudgetInfoVersionHasOneProject) Where(conds ...field.Expr) *pMOBudgetInfoVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoVersionHasOneProject) WithContext(ctx context.Context) *pMOBudgetInfoVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoVersionHasOneProject) Session(session *gorm.Session) *pMOBudgetInfoVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoVersionHasOneProject) Model(m *models.PMOBudgetInfoVersion) *pMOBudgetInfoVersionHasOneProjectTx {
	return &pMOBudgetInfoVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoVersionHasOneProject) Unscoped() *pMOBudgetInfoVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoVersionHasOneProjectTx) Unscoped() *pMOBudgetInfoVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBudgetInfoVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBudgetInfoVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBudgetInfoVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMOBudgetInfoVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoVersionBelongsToCreatedBy) Model(m *models.PMOBudgetInfoVersion) *pMOBudgetInfoVersionBelongsToCreatedByTx {
	return &pMOBudgetInfoVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoVersionBelongsToCreatedBy) Unscoped() *pMOBudgetInfoVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoVersionBelongsToCreatedByTx) Unscoped() *pMOBudgetInfoVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBudgetInfoVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBudgetInfoVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBudgetInfoVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBudgetInfoVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBudgetInfoVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBudgetInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBudgetInfoVersionBelongsToUpdatedBy) Model(m *models.PMOBudgetInfoVersion) *pMOBudgetInfoVersionBelongsToUpdatedByTx {
	return &pMOBudgetInfoVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBudgetInfoVersionBelongsToUpdatedBy) Unscoped() *pMOBudgetInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBudgetInfoVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBudgetInfoVersionBelongsToUpdatedByTx) Unscoped() *pMOBudgetInfoVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBudgetInfoVersionDo struct{ gen.DO }

type IPMOBudgetInfoVersionDo interface {
	gen.SubQuery
	Debug() IPMOBudgetInfoVersionDo
	WithContext(ctx context.Context) IPMOBudgetInfoVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBudgetInfoVersionDo
	WriteDB() IPMOBudgetInfoVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBudgetInfoVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBudgetInfoVersionDo
	Not(conds ...gen.Condition) IPMOBudgetInfoVersionDo
	Or(conds ...gen.Condition) IPMOBudgetInfoVersionDo
	Select(conds ...field.Expr) IPMOBudgetInfoVersionDo
	Where(conds ...gen.Condition) IPMOBudgetInfoVersionDo
	Order(conds ...field.Expr) IPMOBudgetInfoVersionDo
	Distinct(cols ...field.Expr) IPMOBudgetInfoVersionDo
	Omit(cols ...field.Expr) IPMOBudgetInfoVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo
	Group(cols ...field.Expr) IPMOBudgetInfoVersionDo
	Having(conds ...gen.Condition) IPMOBudgetInfoVersionDo
	Limit(limit int) IPMOBudgetInfoVersionDo
	Offset(offset int) IPMOBudgetInfoVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBudgetInfoVersionDo
	Unscoped() IPMOBudgetInfoVersionDo
	Create(values ...*models.PMOBudgetInfoVersion) error
	CreateInBatches(values []*models.PMOBudgetInfoVersion, batchSize int) error
	Save(values ...*models.PMOBudgetInfoVersion) error
	First() (*models.PMOBudgetInfoVersion, error)
	Take() (*models.PMOBudgetInfoVersion, error)
	Last() (*models.PMOBudgetInfoVersion, error)
	Find() ([]*models.PMOBudgetInfoVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBudgetInfoVersion, err error)
	FindInBatches(result *[]*models.PMOBudgetInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBudgetInfoVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBudgetInfoVersionDo
	Assign(attrs ...field.AssignExpr) IPMOBudgetInfoVersionDo
	Joins(fields ...field.RelationField) IPMOBudgetInfoVersionDo
	Preload(fields ...field.RelationField) IPMOBudgetInfoVersionDo
	FirstOrInit() (*models.PMOBudgetInfoVersion, error)
	FirstOrCreate() (*models.PMOBudgetInfoVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOBudgetInfoVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBudgetInfoVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBudgetInfoVersionDo) Debug() IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBudgetInfoVersionDo) WithContext(ctx context.Context) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBudgetInfoVersionDo) ReadDB() IPMOBudgetInfoVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBudgetInfoVersionDo) WriteDB() IPMOBudgetInfoVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBudgetInfoVersionDo) Session(config *gorm.Session) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBudgetInfoVersionDo) Clauses(conds ...clause.Expression) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBudgetInfoVersionDo) Returning(value interface{}, columns ...string) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBudgetInfoVersionDo) Not(conds ...gen.Condition) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBudgetInfoVersionDo) Or(conds ...gen.Condition) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBudgetInfoVersionDo) Select(conds ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBudgetInfoVersionDo) Where(conds ...gen.Condition) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBudgetInfoVersionDo) Order(conds ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBudgetInfoVersionDo) Distinct(cols ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBudgetInfoVersionDo) Omit(cols ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBudgetInfoVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBudgetInfoVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBudgetInfoVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBudgetInfoVersionDo) Group(cols ...field.Expr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBudgetInfoVersionDo) Having(conds ...gen.Condition) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBudgetInfoVersionDo) Limit(limit int) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBudgetInfoVersionDo) Offset(offset int) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBudgetInfoVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBudgetInfoVersionDo) Unscoped() IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBudgetInfoVersionDo) Create(values ...*models.PMOBudgetInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBudgetInfoVersionDo) CreateInBatches(values []*models.PMOBudgetInfoVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBudgetInfoVersionDo) Save(values ...*models.PMOBudgetInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBudgetInfoVersionDo) First() (*models.PMOBudgetInfoVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfoVersion), nil
	}
}

func (p pMOBudgetInfoVersionDo) Take() (*models.PMOBudgetInfoVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfoVersion), nil
	}
}

func (p pMOBudgetInfoVersionDo) Last() (*models.PMOBudgetInfoVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfoVersion), nil
	}
}

func (p pMOBudgetInfoVersionDo) Find() ([]*models.PMOBudgetInfoVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBudgetInfoVersion), err
}

func (p pMOBudgetInfoVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBudgetInfoVersion, err error) {
	buf := make([]*models.PMOBudgetInfoVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBudgetInfoVersionDo) FindInBatches(result *[]*models.PMOBudgetInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBudgetInfoVersionDo) Attrs(attrs ...field.AssignExpr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBudgetInfoVersionDo) Assign(attrs ...field.AssignExpr) IPMOBudgetInfoVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBudgetInfoVersionDo) Joins(fields ...field.RelationField) IPMOBudgetInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBudgetInfoVersionDo) Preload(fields ...field.RelationField) IPMOBudgetInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBudgetInfoVersionDo) FirstOrInit() (*models.PMOBudgetInfoVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfoVersion), nil
	}
}

func (p pMOBudgetInfoVersionDo) FirstOrCreate() (*models.PMOBudgetInfoVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBudgetInfoVersion), nil
	}
}

func (p pMOBudgetInfoVersionDo) FindByPage(offset int, limit int) (result []*models.PMOBudgetInfoVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBudgetInfoVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBudgetInfoVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBudgetInfoVersionDo) Delete(models ...*models.PMOBudgetInfoVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBudgetInfoVersionDo) withDO(do gen.Dao) *pMOBudgetInfoVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
