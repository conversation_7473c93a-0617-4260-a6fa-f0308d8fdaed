// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newTeam(db *gorm.DB, opts ...gen.DOOption) team {
	_team := team{}

	_team.teamDo.UseDB(db, opts...)
	_team.teamDo.UseModel(&models.Team{})

	tableName := _team.teamDo.TableName()
	_team.ALL = field.NewAsterisk(tableName)
	_team.ID = field.NewString(tableName, "id")
	_team.CreatedAt = field.NewTime(tableName, "created_at")
	_team.UpdatedAt = field.NewTime(tableName, "updated_at")
	_team.DeletedAt = field.NewField(tableName, "deleted_at")
	_team.Name = field.NewString(tableName, "name")
	_team.Code = field.NewString(tableName, "code")
	_team.Color = field.NewString(tableName, "color")
	_team.Description = field.NewString(tableName, "description")
	_team.WorkingStartAt = field.NewString(tableName, "working_start_at")
	_team.WorkingEndAt = field.NewString(tableName, "working_end_at")
	_team.CreatedByID = field.NewString(tableName, "created_by_id")
	_team.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_team.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_team.Users = teamHasManyUsers{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Users", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Users.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Users.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Users.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Users.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Checkins.User", "models.User"),
			},
		},
	}

	_team.fillFieldMap()

	return _team
}

type team struct {
	teamDo

	ALL            field.Asterisk
	ID             field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	Name           field.String
	Code           field.String
	Color          field.String
	Description    field.String
	WorkingStartAt field.String
	WorkingEndAt   field.String
	CreatedByID    field.String
	UpdatedByID    field.String
	DeletedByID    field.String
	Users          teamHasManyUsers

	fieldMap map[string]field.Expr
}

func (t team) Table(newTableName string) *team {
	t.teamDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t team) As(alias string) *team {
	t.teamDo.DO = *(t.teamDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *team) updateTableName(table string) *team {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.DeletedAt = field.NewField(table, "deleted_at")
	t.Name = field.NewString(table, "name")
	t.Code = field.NewString(table, "code")
	t.Color = field.NewString(table, "color")
	t.Description = field.NewString(table, "description")
	t.WorkingStartAt = field.NewString(table, "working_start_at")
	t.WorkingEndAt = field.NewString(table, "working_end_at")
	t.CreatedByID = field.NewString(table, "created_by_id")
	t.UpdatedByID = field.NewString(table, "updated_by_id")
	t.DeletedByID = field.NewString(table, "deleted_by_id")

	t.fillFieldMap()

	return t
}

func (t *team) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *team) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 14)
	t.fieldMap["id"] = t.ID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["deleted_at"] = t.DeletedAt
	t.fieldMap["name"] = t.Name
	t.fieldMap["code"] = t.Code
	t.fieldMap["color"] = t.Color
	t.fieldMap["description"] = t.Description
	t.fieldMap["working_start_at"] = t.WorkingStartAt
	t.fieldMap["working_end_at"] = t.WorkingEndAt
	t.fieldMap["created_by_id"] = t.CreatedByID
	t.fieldMap["updated_by_id"] = t.UpdatedByID
	t.fieldMap["deleted_by_id"] = t.DeletedByID

}

func (t team) clone(db *gorm.DB) team {
	t.teamDo.ReplaceConnPool(db.Statement.ConnPool)
	t.Users.db = db.Session(&gorm.Session{Initialized: true})
	t.Users.db.Statement.ConnPool = db.Statement.ConnPool
	return t
}

func (t team) replaceDB(db *gorm.DB) team {
	t.teamDo.ReplaceDB(db)
	t.Users.db = db.Session(&gorm.Session{})
	return t
}

type teamHasManyUsers struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a teamHasManyUsers) Where(conds ...field.Expr) *teamHasManyUsers {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a teamHasManyUsers) WithContext(ctx context.Context) *teamHasManyUsers {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a teamHasManyUsers) Session(session *gorm.Session) *teamHasManyUsers {
	a.db = a.db.Session(session)
	return &a
}

func (a teamHasManyUsers) Model(m *models.Team) *teamHasManyUsersTx {
	return &teamHasManyUsersTx{a.db.Model(m).Association(a.Name())}
}

func (a teamHasManyUsers) Unscoped() *teamHasManyUsers {
	a.db = a.db.Unscoped()
	return &a
}

type teamHasManyUsersTx struct{ tx *gorm.Association }

func (a teamHasManyUsersTx) Find() (result []*models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a teamHasManyUsersTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a teamHasManyUsersTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a teamHasManyUsersTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a teamHasManyUsersTx) Clear() error {
	return a.tx.Clear()
}

func (a teamHasManyUsersTx) Count() int64 {
	return a.tx.Count()
}

func (a teamHasManyUsersTx) Unscoped() *teamHasManyUsersTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type teamDo struct{ gen.DO }

type ITeamDo interface {
	gen.SubQuery
	Debug() ITeamDo
	WithContext(ctx context.Context) ITeamDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITeamDo
	WriteDB() ITeamDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITeamDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITeamDo
	Not(conds ...gen.Condition) ITeamDo
	Or(conds ...gen.Condition) ITeamDo
	Select(conds ...field.Expr) ITeamDo
	Where(conds ...gen.Condition) ITeamDo
	Order(conds ...field.Expr) ITeamDo
	Distinct(cols ...field.Expr) ITeamDo
	Omit(cols ...field.Expr) ITeamDo
	Join(table schema.Tabler, on ...field.Expr) ITeamDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITeamDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITeamDo
	Group(cols ...field.Expr) ITeamDo
	Having(conds ...gen.Condition) ITeamDo
	Limit(limit int) ITeamDo
	Offset(offset int) ITeamDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamDo
	Unscoped() ITeamDo
	Create(values ...*models.Team) error
	CreateInBatches(values []*models.Team, batchSize int) error
	Save(values ...*models.Team) error
	First() (*models.Team, error)
	Take() (*models.Team, error)
	Last() (*models.Team, error)
	Find() ([]*models.Team, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Team, err error)
	FindInBatches(result *[]*models.Team, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Team) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITeamDo
	Assign(attrs ...field.AssignExpr) ITeamDo
	Joins(fields ...field.RelationField) ITeamDo
	Preload(fields ...field.RelationField) ITeamDo
	FirstOrInit() (*models.Team, error)
	FirstOrCreate() (*models.Team, error)
	FindByPage(offset int, limit int) (result []*models.Team, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITeamDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t teamDo) Debug() ITeamDo {
	return t.withDO(t.DO.Debug())
}

func (t teamDo) WithContext(ctx context.Context) ITeamDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t teamDo) ReadDB() ITeamDo {
	return t.Clauses(dbresolver.Read)
}

func (t teamDo) WriteDB() ITeamDo {
	return t.Clauses(dbresolver.Write)
}

func (t teamDo) Session(config *gorm.Session) ITeamDo {
	return t.withDO(t.DO.Session(config))
}

func (t teamDo) Clauses(conds ...clause.Expression) ITeamDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t teamDo) Returning(value interface{}, columns ...string) ITeamDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t teamDo) Not(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t teamDo) Or(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t teamDo) Select(conds ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t teamDo) Where(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t teamDo) Order(conds ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t teamDo) Distinct(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t teamDo) Omit(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t teamDo) Join(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t teamDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t teamDo) RightJoin(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t teamDo) Group(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t teamDo) Having(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t teamDo) Limit(limit int) ITeamDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t teamDo) Offset(offset int) ITeamDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t teamDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t teamDo) Unscoped() ITeamDo {
	return t.withDO(t.DO.Unscoped())
}

func (t teamDo) Create(values ...*models.Team) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t teamDo) CreateInBatches(values []*models.Team, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t teamDo) Save(values ...*models.Team) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t teamDo) First() (*models.Team, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Team), nil
	}
}

func (t teamDo) Take() (*models.Team, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Team), nil
	}
}

func (t teamDo) Last() (*models.Team, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Team), nil
	}
}

func (t teamDo) Find() ([]*models.Team, error) {
	result, err := t.DO.Find()
	return result.([]*models.Team), err
}

func (t teamDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Team, err error) {
	buf := make([]*models.Team, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t teamDo) FindInBatches(result *[]*models.Team, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t teamDo) Attrs(attrs ...field.AssignExpr) ITeamDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t teamDo) Assign(attrs ...field.AssignExpr) ITeamDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t teamDo) Joins(fields ...field.RelationField) ITeamDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t teamDo) Preload(fields ...field.RelationField) ITeamDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t teamDo) FirstOrInit() (*models.Team, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Team), nil
	}
}

func (t teamDo) FirstOrCreate() (*models.Team, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Team), nil
	}
}

func (t teamDo) FindByPage(offset int, limit int) (result []*models.Team, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t teamDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t teamDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t teamDo) Delete(models ...*models.Team) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *teamDo) withDO(do gen.Dao) *teamDo {
	t.DO = *do.(*gen.DO)
	return t
}
