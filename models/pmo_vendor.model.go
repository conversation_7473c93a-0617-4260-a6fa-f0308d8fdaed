package models

// PMOVendor<PERSON>tem represents project vendor items
type PMOVendorItem struct {
	BaseModel
	ProjectID          string `json:"project_id" gorm:"column:project_id;type:uuid"`
	VendorName         string `json:"vendor_name" gorm:"column:vendor_name"`
	ItemName           string `json:"item_name" gorm:"column:item_name"`
	ItemDetail         string `json:"item_detail" gorm:"column:item_detail"`
	DeliverDurationDay int64  `json:"deliver_duration_day" gorm:"column:deliver_duration_day"`
	IsTor              bool   `json:"is_tor" gorm:"column:is_tor"`
	IsImplementation   bool   `json:"is_implementation" gorm:"column:is_implementation"`
	IsTraining         bool   `json:"is_training" gorm:"column:is_training"`
	IsUserManual       bool   `json:"is_user_manual" gorm:"column:is_user_manual"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOVendorItem) TableName() string {
	return "pmo_vendor_items"
}
