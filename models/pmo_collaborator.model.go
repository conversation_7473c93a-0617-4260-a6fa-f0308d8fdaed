package models

// PMOCollaborator represents project collaborators
type PMOCollaborator struct {
	BaseModel
	ProjectID              string           `json:"project_id" gorm:"column:project_id;type:uuid"`
	UserID                 string           `json:"user_id" gorm:"column:user_id;type:uuid"`
	ConfidentialPermission PMOTabPermission `json:"confidential_permission" gorm:"column:confidential_permission"`
	ConfidentialMain       bool             `json:"confidential_main" gorm:"column:confidential_main"`
	SalesPermission        PMOTabPermission `json:"sales_permission" gorm:"column:sales_permission"`
	SalesMain              bool             `json:"sales_main" gorm:"column:sales_main"`
	PresalesPermission     PMOTabPermission `json:"presales_permission" gorm:"column:presales_permission"`
	PresalesMain           bool             `json:"presales_main" gorm:"column:presales_main"`
	BiddingPermission      PMOTabPermission `json:"bidding_permission" gorm:"column:bidding_permission"`
	BiddingMain            bool             `json:"bidding_main" gorm:"column:bidding_main"`
	PMOPermission          PMOTabPermission `json:"pmo_permission" gorm:"column:pmo_permission"`
	PM<PERSON>ain                bool             `json:"pmo_main" gorm:"column:pmo_main"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project   *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	User      *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

func (PMOCollaborator) TableName() string {
	return "pmo_collaborators"
}
