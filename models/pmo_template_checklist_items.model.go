package models

type PMOTemplateChecklistItem struct {
	BaseModel
	TabKey      PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail      string    `json:"detail" gorm:"column:detail"`
	CreatedByID *string   `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string   `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string   `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`
}

func (PMOTemplateChecklistItem) TableName() string {
	return "pmo_template_checklist_items"
}
