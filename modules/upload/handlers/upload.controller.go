package handlers

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/asaskevich/govalidator"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/upload/services"
	core "gitlab.finema.co/finema/idin-core"
)

type UploadController struct{}

func (m UploadController) Upload(c core.IHTTPContext) error {
	fileHeader, err := c.FormFile("file")
	if err != nil {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_REQUIRED",
			"message": "file is required",
		})
	}

	var app models.FileAppKey = models.FileAppKeyCommon
	if appValue := c.FormValue("app"); appValue != "" {
		apps := []string{
			string(models.FileAppKeyPMO),
			string(models.FileAppKeyClockin),
			string(models.FileAppKeyTimesheet),
			string(models.FileAppKeyCommon),
		}
		isValid := govalidator.IsIn(appValue, apps...)
		if !isValid {
			return c.JSON(http.StatusBadRequest, core.Map{
				"code":    "INVALID_APP",
				"message": fmt.Sprintf("app must be one of %v", strings.Join(apps, ", ")),
				"data":    apps,
			})
		}
		app = models.FileAppKey(appValue)
	}

	uploadSvc := services.NewUploadService(c)
	res, ierr := uploadSvc.UploadFile(fileHeader, app)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m UploadController) GetFile(c core.IHTTPContext) error {
	// Get the file path from the URL parameter
	filePath := c.Param("*")
	if filePath == "" {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_PATH_REQUIRED",
			"message": "file path is required",
		})
	}

	uploadSvc := services.NewUploadService(c)
	fileReader, contentType, ierr := uploadSvc.GetFile(filePath)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}
	defer fileReader.Close()

	// Set appropriate headers
	c.Response().Header().Set("Content-Type", contentType)

	// Stream the file content
	return c.Stream(http.StatusOK, contentType, fileReader)
}

func (m UploadController) GetFileMeta(c core.IHTTPContext) error {
	// Get the file ID or path from the URL parameter
	idOrPath := c.Param("*")
	if idOrPath == "" {
		return c.JSON(http.StatusBadRequest, core.Map{
			"code":    "FILE_ID_OR_PATH_REQUIRED",
			"message": "file ID or path is required",
		})
	}

	uploadSvc := services.NewUploadService(c)
	file, ierr := uploadSvc.GetFileMeta(idOrPath)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, file)
}
