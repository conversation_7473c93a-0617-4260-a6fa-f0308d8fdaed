package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectCreate struct {
	core.BaseValidator
	Name           *string `json:"name"`
	Code           *string `json:"code"`
	Description    *string `json:"description"`
	StartWorkingAt *string `json:"working_start_at"`
	EndWorkingAt   *string `json:"working_end_at"`
}

func (r *ProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))

	if r.Must(r.IsStrRequired(r.Code, "code")) {
		codeValue := utils.ToNonPointer(r.Code)
		if codeValue != strings.ToUpper(codeValue) {
			r.Must(false, &core.IValidMessage{
				Name:    "code",
				Code:    "UPPERCASE_REQUIRED",
				Message: "Code must be uppercase",
			})
		}
		if strings.Contains(codeValue, " ") {
			r.Must(false, &core.IValidMessage{
				Name:    "code",
				Code:    "NO_SPACES_ALLOWED",
				Message: "Code must not contain spaces",
			})
		}
	}

	r.Must(r.IsStrUnique(ctx, r.Name, models.Project{}.TableName(), "name", "", "name"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Project{}.TableName(), "code", "", "code"))

	return r.Error()
}
