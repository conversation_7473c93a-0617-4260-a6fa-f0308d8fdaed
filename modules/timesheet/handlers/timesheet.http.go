package handlers

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewTimesheetHTTP(e *echo.Echo) {
	timesheet := &TimesheetController{}
	e.GET("/timesheets", core.WithHTTPContext(timesheet.Pagination), middleware.AuthMiddleware())
	e.GET("/timesheets/:id", core.WithHTTPContext(timesheet.Find), middleware.AuthMiddleware())
	e.POST("/timesheets", core.WithHTTPContext(timesheet.Create), middleware.AuthMiddleware())
	e.PUT("/timesheets/:id", core.WithHTTPContext(timesheet.Update), middleware.AuthMiddleware())
	e.DELETE("/timesheets/:id", core.WithHTTPContext(timesheet.Delete), middleware.AuthMiddleware())

	timesheetAdmin := &TimesheetAdminController{}
	e.GET("/admin/timesheets", core.WithHTTPContext(timesheetAdmin.Pagination), middleware.AuthMiddleware())
	e.GET("/admin/timesheets/summary-report", core.WithHTTPContext(timesheetAdmin.SummaryReport), middleware.AuthMiddleware())
	e.GET("/admin/timesheets/:id", core.WithHTTPContext(timesheetAdmin.Find), middleware.AuthMiddleware())
}
