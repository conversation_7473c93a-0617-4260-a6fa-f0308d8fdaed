package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Holiday = repository.Make[models.Holiday]()

func HolidayOrderBy(pageOptions *core.PageOptions) repository.Option[models.Holiday] {
	return func(c repository.IRepository[models.Holiday]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func HolidayWithSearch(q string) repository.Option[models.Holiday] {
	return func(c repository.IRepository[models.Holiday]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}
