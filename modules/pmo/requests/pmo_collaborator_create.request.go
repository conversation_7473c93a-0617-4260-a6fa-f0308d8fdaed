package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOCollaboratorCreate struct {
	core.BaseValidator
	UserID                 *string `json:"user_id"`
	ConfidentialPermission *string `json:"confidential_permission"`
	ConfidentialMain       *bool   `json:"confidential_main"`
	SalesPermission        *string `json:"sales_permission"`
	SalesMain              *bool   `json:"sales_main"`
	PresalesPermission     *string `json:"presales_permission"`
	PresalesMain           *bool   `json:"presales_main"`
	BiddingPermission      *string `json:"bidding_permission"`
	BiddingMain            *bool   `json:"bidding_main"`
	PMOPermission          *string `json:"pmo_permission"`
	PMOMain                *bool   `json:"pmo_main"`
}

func (r *PMOCollaboratorCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>equired(r.<PERSON>, "user_id"))

	// Validate permission values
	r.Must(r.IsStrIn(r.ConfidentialPermission, strings.Join(models.PMOTabPermissions, "|"), "confidential_permission"))
	r.Must(r.IsStrIn(r.SalesPermission, strings.Join(models.PMOTabPermissions, "|"), "sales_permission"))
	r.Must(r.IsStrIn(r.PresalesPermission, strings.Join(models.PMOTabPermissions, "|"), "presales_permission"))
	r.Must(r.IsStrIn(r.BiddingPermission, strings.Join(models.PMOTabPermissions, "|"), "bidding_permission"))
	r.Must(r.IsStrIn(r.PMOPermission, strings.Join(models.PMOTabPermissions, "|"), "pmo_permission"))

	// Check if user exists
	r.Must(r.IsExists(ctx, r.UserID, models.User{}.TableName(), "id", "user_id"))

	return r.Error()
}
