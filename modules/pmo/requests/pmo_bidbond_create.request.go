package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOBidbondCreate struct {
	core.BaseValidator
	GuaranteeAsset *string  `json:"guarantee_asset"`
	BidbondPayer   *string  `json:"bidbond_payer"`
	BidbondValue   *float64 `json:"bidbond_value"`
	StartDate      *string  `json:"start_date"`
	EndDate        *string  `json:"end_date"`
	DurationMonth  *int64   `json:"duration_month"`
	DurationYear   *int64   `json:"duration_year"`
	Fee            *float64 `json:"fee"`
}

func (r *PMOBidbondCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.<PERSON>ee<PERSON>, "guarantee_asset"))
	r.Must(r.IsStrRequired(r.<PERSON>idbondPayer, "bidbond_payer"))
	r.Must(r.<PERSON>equired(r.<PERSON>, "bidbond_value"))
	r.Must(r.Is<PERSON>equired(r.Start<PERSON>ate, "start_date"))
	r.Must(r.Is<PERSON>equired(r.EndDate, "end_date"))
	r.Must(r.Is<PERSON>ate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))
	r.Must(r.IsRequired(r.DurationMonth, "duration_month"))
	r.Must(r.IsRequired(r.DurationYear, "duration_year"))
	r.Must(r.IsRequired(r.Fee, "fee"))

	return r.Error()
}
