package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectChecklistItemCreate struct {
	core.BaseValidator
	TabKey    *string `json:"tab_key"`
	Detail    *string `json:"detail"`
	IsChecked *bool   `json:"is_checked"`
}

func (r *PMOProjectChecklistItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>StrRequired(r.Tab<PERSON><PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.Detail, "detail"))

	r.Must(r.IsStrIn(r.<PERSON><PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
