package requests

import (
	"strings"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentItemCreate struct {
	core.BaseValidator
	TabKey        *string    `json:"tab_key"`
	GroupID       *string    `json:"group_id"`
	Name          *string    `json:"name"`
	SharepointURL *string    `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          *string    `json:"type"`
	FileID        *string    `json:"file_id"`
}

func (r *PMODocumentItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.<PERSON>b<PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.GroupID, "group_id"))
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.<PERSON>hare<PERSON>, "sharepoint_url"))
	r.Must(r.<PERSON>equired(r.<PERSON>, "type"))

	r.Must(r.<PERSON>n(r.<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))
	r.Must(r.IsStrIn(r.Type, strings.Join(models.PMODocTypes, "|"), "type"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))
	r.Must(r.IsExists(ctx, r.GroupID, models.PMODocumentGroup{}.TableName(), "id", "group_id"))

		r.Must(r.IsExists(ctx, r.FileID, models.File{}.TableName(), "id", "file_id"))

	return r.Error()
}
