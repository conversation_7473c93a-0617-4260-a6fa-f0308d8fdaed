package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOContactCreate struct {
	core.BaseValidator
	Fullname *string `json:"fullname"`
	Phone    *string `json:"phone"`
	Email    *string `json:"email"`
	Detail   *string `json:"detail"`
	Company  *string `json:"company"`
	Position *string `json:"position"`
}

func (r *PMOContactCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Fullname, "fullname"))
	r.Must(r.<PERSON>mail(r.<PERSON>ail, "email"))

	return r.Error()
}
