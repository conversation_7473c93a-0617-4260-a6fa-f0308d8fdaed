package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOTemplateChecklistItemPaginationRequest struct {
	core.BaseValidator
	TabKey *string `json:"tab_key" query:"tab_key"`
}

func (r *PMOTemplateChecklistItemPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Tab<PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
