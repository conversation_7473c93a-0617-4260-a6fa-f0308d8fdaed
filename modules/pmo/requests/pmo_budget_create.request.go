package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOBudgetCreate struct {
	core.BaseValidator
	FundType     *string  `json:"fund_type"`
	ProjectValue *float64 `json:"project_value"`
	BidbondValue *float64 `json:"bidbond_value"`
	Partner      *string  `json:"partner"`
}

func (r *PMOBudgetCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.FundType, "fund_type"))
	r.Must(r.<PERSON>StrRequired(r.<PERSON>, "partner"))
	r.Must(r.IsRequired(r.ProjectValue, "project_value"))
	r.Must(r.IsRequired(r.<PERSON><PERSON>bondValue, "bidbond_value"))

	return r.Error()
}
