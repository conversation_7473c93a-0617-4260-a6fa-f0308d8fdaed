package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOBiddingCreate struct {
	core.BaseValidator
	BiddingType  *string  `json:"bidding_type"`
	BiddingValue *float64 `json:"bidding_value"`
	TenderDate   *string  `json:"tender_date"`
	TenderEntity *string  `json:"tender_entity"`
	AnnounceDate *string  `json:"announce_date"`
}

func (r *PMOBiddingCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.BiddingType, "bidding_type"))
	r.Must(r.IsRequired(r.<PERSON>id<PERSON>al<PERSON>, "bidding_value"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.<PERSON>, "tender_entity"))
	r.Must(r.IsDate(r.TenderDate, "tender_date"))
	r.Must(r.IsDate(r.AnnounceDate, "announce_date"))

	return r.Error()
}
