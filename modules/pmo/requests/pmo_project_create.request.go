package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectCreate struct {
	core.BaseValidator
	Name      *string  `json:"name"`
	Slug      *string  `json:"slug"`
	Email     *string  `json:"email"`
	Tags      []string `json:"tags"`
	ProjectID *string  `json:"project_id"`
}

func (r *PMOProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Slug, "slug"))

	r.Must(r.IsEmail(r.Email, "email"))
	// UUID validation is handled by IsExists check

	r.Must(r.IsStrUnique(ctx, r.Name, models.PMOProject{}.TableName(), "name", "", "name"))
	r.Must(r.<PERSON>(ctx, r.Slug, models.PMOProject{}.TableName(), "slug", "", "slug"))

	r.Must(r.<PERSON>(ctx, r.ProjectID, models.Project{}.TableName(), "id", "project_id"))
	r.Must(r.IsStrUnique(ctx, r.ProjectID, models.PMOProject{}.TableName(), "project_id", "", "project_id"))

	return r.Error()
}
