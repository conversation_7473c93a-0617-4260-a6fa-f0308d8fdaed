package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentGroupPaginationRequest struct {
	core.BaseValidator
	TabKey *string `json:"tab_key" query:"tab_key"`
}

func (r *PMODocumentGroupPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.<PERSON>b<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
