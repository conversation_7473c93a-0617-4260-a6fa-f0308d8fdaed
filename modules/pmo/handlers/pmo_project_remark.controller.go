package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectRemarkController struct {
}

// PMO Project Remark methods
func (m PMOProjectRemarkController) RemarkFind(c core.IHTTPContext) error {
	input := &requests.PMORemarkFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoRemarkSvc := services.NewPMOProjectRemarkService(c)
	remark, err := pmoRemarkSvc.Find(c.Param("id"), models.PMOTabKey(utils.ToNonPointer(input.TabKey)))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, remark)
}

func (m PMOProjectRemarkController) RemarksCreate(c core.IHTTPContext) error {
	input := &requests.PMORemarkCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoRemarkSvc := services.NewPMOProjectRemarkService(c)
	payload := &dto.PMORemarkCreatePayload{
		ProjectID: c.Param("id"), // Set project ID from URL parameter
		TabKey:    models.PMOTabKey(utils.ToNonPointer(input.TabKey)),
		Detail:    utils.ToNonPointer(input.Detail),
	}

	remark, err := pmoRemarkSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, remark)
}

func (m PMOProjectRemarkController) RemarksVersions(c core.IHTTPContext) error {
	input := &requests.PMORemarkFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}
	pmoRemarkSvc := services.NewPMOProjectRemarkService(c)
	res, err := pmoRemarkSvc.VersionsPagination(c.Param("id"), models.PMOTabKey(utils.ToNonPointer(input.TabKey)), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
