package handlers

import (
	"net/http"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectLGController struct {
}

// PMO Project LG methods
func (m PMOProjectLGController) LGFind(c core.IHTTPContext) error {
	input := &requests.PMOLGFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoLGSvc := services.NewPMOProjectLGService(c)
	lg, err := pmoLGSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, lg)
}

func (m PMOProjectLGController) LGCreate(c core.IHTTPContext) error {
	input := &requests.PMOLGCreate{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoLGSvc := services.NewPMOProjectLGService(c)
	startDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.StartDate))
	endDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.EndDate))
	payload := &dto.PMOLGCreatePayload{
		ProjectID: c.Param("id"),
		Value:     utils.ToNonPointer(input.Value),
		StartDate: &startDate,
		EndDate:   &endDate,
		Fee:       utils.ToNonPointer(input.Fee),
		Interest:  utils.ToNonPointer(input.Interest),
	}

	lg, err := pmoLGSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, lg)
}

func (m PMOProjectLGController) LGVersionsFind(c core.IHTTPContext) error {
	pmoLGSvc := services.NewPMOProjectLGService(c)
	res, err := pmoLGSvc.LGVersionsFind(c.Param("id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
