package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectVendorItemController struct {
}

// PMO Project Vendor Items methods
func (m PMOProjectVendorItemController) VendorItemsPagination(c core.IHTTPContext) error {
	input := &requests.PMOVendorItemPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoVendorItemSvc := services.NewPMOProjectVendorItemService(c)
	res, ierr := pmoVendorItemSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOVendorItemPaginationOptions{})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectVendorItemController) VendorItemsFind(c core.IHTTPContext) error {
	pmoVendorItemSvc := services.NewPMOProjectVendorItemService(c)
	vendorItem, err := pmoVendorItemSvc.Find(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, vendorItem)
}

func (m PMOProjectVendorItemController) VendorItemsCreate(c core.IHTTPContext) error {
	input := &requests.PMOVendorItemCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoVendorItemSvc := services.NewPMOProjectVendorItemService(c)
	payload := &dto.PMOVendorItemCreatePayload{
		ProjectID:          c.Param("id"),
		VendorName:         utils.ToNonPointer(input.VendorName),
		ItemName:           utils.ToNonPointer(input.ItemName),
		ItemDetail:         utils.ToNonPointer(input.ItemDetail),
		DeliverDurationDay: utils.ToNonPointer(input.DeliverDurationDay),
		IsTor:              utils.ToNonPointer(input.IsTor),
		IsImplementation:   utils.ToNonPointer(input.IsImplementation),
		IsTraining:         utils.ToNonPointer(input.IsTraining),
		IsUserManual:       utils.ToNonPointer(input.IsUserManual),
	}

	vendorItem, err := pmoVendorItemSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, vendorItem)
}

func (m PMOProjectVendorItemController) VendorItemsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOVendorItemUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoVendorItemSvc := services.NewPMOProjectVendorItemService(c)
	payload := &dto.PMOVendorItemUpdatePayload{
		VendorName:         utils.ToNonPointer(input.VendorName),
		ItemName:           utils.ToNonPointer(input.ItemName),
		ItemDetail:         utils.ToNonPointer(input.ItemDetail),
		DeliverDurationDay: utils.ToNonPointer(input.DeliverDurationDay),
		IsTor:              utils.ToNonPointer(input.IsTor),
		IsImplementation:   utils.ToNonPointer(input.IsImplementation),
		IsTraining:         utils.ToNonPointer(input.IsTraining),
		IsUserManual:       utils.ToNonPointer(input.IsUserManual),
	}

	vendorItem, err := pmoVendorItemSvc.Update(c.Param("item_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, vendorItem)
}

func (m PMOProjectVendorItemController) VendorItemsDelete(c core.IHTTPContext) error {
	pmoVendorItemSvc := services.NewPMOProjectVendorItemService(c)
	err := pmoVendorItemSvc.Delete(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
