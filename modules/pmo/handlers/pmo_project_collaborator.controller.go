package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectCollaboratorController struct {
}

// PMO Project Collaborators methods
func (m PMOProjectCollaboratorController) CollaboratorsPagination(c core.IHTTPContext) error {
	input := &requests.PMOCollaboratorPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoCollaboratorSvc := services.NewPMOProjectCollaboratorService(c)
	res, ierr := pmoCollaboratorSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOCollaboratorPaginationOptions{
		TabKey: input.TabKey,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectCollaboratorController) CollaboratorsFind(c core.IHTTPContext) error {
	pmoCollaboratorSvc := services.NewPMOProjectCollaboratorService(c)
	collaborator, err := pmoCollaboratorSvc.Find(c.Param("collaborator_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, collaborator)
}

func (m PMOProjectCollaboratorController) CollaboratorsCreate(c core.IHTTPContext) error {
	input := &requests.PMOCollaboratorCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoCollaboratorSvc := services.NewPMOProjectCollaboratorService(c)
	payload := &dto.PMOCollaboratorCreatePayload{
		ProjectID:              c.Param("id"), // Set project ID from URL parameter
		UserID:                 utils.ToNonPointer(input.UserID),
		ConfidentialPermission: m.getPMOTabPermission(input.ConfidentialPermission),
		ConfidentialMain:       input.ConfidentialMain,
		SalesPermission:        m.getPMOTabPermission(input.SalesPermission),
		SalesMain:              input.SalesMain,
		PresalesPermission:     m.getPMOTabPermission(input.PresalesPermission),
		PresalesMain:           input.PresalesMain,
		BiddingPermission:      m.getPMOTabPermission(input.BiddingPermission),
		BiddingMain:            input.BiddingMain,
		PMOPermission:          m.getPMOTabPermission(input.PMOPermission),
		PMOMain:                input.PMOMain,
	}

	collaborator, err := pmoCollaboratorSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, collaborator)
}

func (m PMOProjectCollaboratorController) CollaboratorsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOCollaboratorUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoCollaboratorSvc := services.NewPMOProjectCollaboratorService(c)
	// Permission can nil
	payload := &dto.PMOCollaboratorUpdatePayload{
		ConfidentialPermission: m.getPMOTabPermission(input.ConfidentialPermission),
		ConfidentialMain:       input.ConfidentialMain,
		SalesPermission:        m.getPMOTabPermission(input.SalesPermission),
		SalesMain:              input.SalesMain,
		PresalesPermission:     m.getPMOTabPermission(input.PresalesPermission),
		PresalesMain:           input.PresalesMain,
		BiddingPermission:      m.getPMOTabPermission(input.BiddingPermission),
		BiddingMain:            input.BiddingMain,
		PMOPermission:          m.getPMOTabPermission(input.PMOPermission),
		PMOMain:                input.PMOMain,
	}

	collaborator, err := pmoCollaboratorSvc.Update(c.Param("collaborator_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, collaborator)
}

func (m PMOProjectCollaboratorController) CollaboratorsDelete(c core.IHTTPContext) error {
	pmoCollaboratorSvc := services.NewPMOProjectCollaboratorService(c)
	err := pmoCollaboratorSvc.Delete(c.Param("collaborator_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m PMOProjectCollaboratorController) getPMOTabPermission(input *string) *models.PMOTabPermission {
	if input == nil {
		return nil
	}
	permission := models.PMOTabPermission(utils.ToNonPointer(input))
	return &permission
}
