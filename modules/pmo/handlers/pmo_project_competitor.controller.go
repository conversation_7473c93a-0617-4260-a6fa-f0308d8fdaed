package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectCompetitorController struct {
}

// PMO Project Competitors methods
func (m PMOProjectCompetitorController) CompetitorsPagination(c core.IHTTPContext) error {
	input := &requests.PMOCompetitorPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoCompetitorSvc := services.NewPMOProjectCompetitorService(c)
	res, ierr := pmoCompetitorSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOCompetitorPaginationOptions{})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectCompetitorController) CompetitorsFind(c core.IHTTPContext) error {
	pmoCompetitorSvc := services.NewPMOProjectCompetitorService(c)
	competitor, err := pmoCompetitorSvc.Find(c.Param("competitor_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, competitor)
}

func (m PMOProjectCompetitorController) CompetitorsCreate(c core.IHTTPContext) error {
	input := &requests.PMOCompetitorCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoCompetitorSvc := services.NewPMOProjectCompetitorService(c)
	payload := &dto.PMOCompetitorCreatePayload{
		ProjectID: c.Param("id"),
		Company:   utils.ToNonPointer(input.Company),
		Detail:    utils.ToNonPointer(input.Detail),
	}

	competitor, err := pmoCompetitorSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, competitor)
}

func (m PMOProjectCompetitorController) CompetitorsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOCompetitorUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoCompetitorSvc := services.NewPMOProjectCompetitorService(c)
	payload := &dto.PMOCompetitorUpdatePayload{
		Company: utils.ToNonPointer(input.Company),
		Detail:  utils.ToNonPointer(input.Detail),
	}

	competitor, err := pmoCompetitorSvc.Update(c.Param("competitor_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, competitor)
}

func (m PMOProjectCompetitorController) CompetitorsDelete(c core.IHTTPContext) error {
	pmoCompetitorSvc := services.NewPMOProjectCompetitorService(c)
	err := pmoCompetitorSvc.Delete(c.Param("competitor_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
