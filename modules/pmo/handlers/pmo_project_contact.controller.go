package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectContactController struct {
}

// PMO Project Contacts methods
func (m PMOProjectContactController) ContactsPagination(c core.IHTTPContext) error {
	input := &requests.PMOContactPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoContactSvc := services.NewPMOProjectContactService(c)
	res, ierr := pmoContactSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOContactPaginationOptions{})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectContactController) ContactsFind(c core.IHTTPContext) error {
	pmoContactSvc := services.NewPMOProjectContactService(c)
	contact, err := pmoContactSvc.Find(c.Param("contact_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, contact)
}

func (m PMOProjectContactController) ContactsCreate(c core.IHTTPContext) error {
	input := &requests.PMOContactCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoContactSvc := services.NewPMOProjectContactService(c)
	payload := &dto.PMOContactCreatePayload{
		ProjectID: c.Param("id"),
		Fullname:  utils.ToNonPointer(input.Fullname),
		Phone:     utils.ToNonPointer(input.Phone),
		Email:     utils.ToNonPointer(input.Email),
		Detail:    utils.ToNonPointer(input.Detail),
		Company:   utils.ToNonPointer(input.Company),
		Position:  utils.ToNonPointer(input.Position),
	}

	contact, err := pmoContactSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, contact)
}

func (m PMOProjectContactController) ContactsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOContactUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoContactSvc := services.NewPMOProjectContactService(c)
	payload := &dto.PMOContactUpdatePayload{
		Fullname: utils.ToNonPointer(input.Fullname),
		Phone:    utils.ToNonPointer(input.Phone),
		Email:    utils.ToNonPointer(input.Email),
		Detail:   utils.ToNonPointer(input.Detail),
		Company:  utils.ToNonPointer(input.Company),
		Position: utils.ToNonPointer(input.Position),
	}

	contact, err := pmoContactSvc.Update(c.Param("contact_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, contact)
}

func (m PMOProjectContactController) ContactsDelete(c core.IHTTPContext) error {
	pmoContactSvc := services.NewPMOProjectContactService(c)
	err := pmoContactSvc.Delete(c.Param("contact_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
