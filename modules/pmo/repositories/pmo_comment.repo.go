package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Comment Repository
var PMOComment = repository.Make[models.PMOComment]()

func PMOCommentOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at ASC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentWithRelations() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("Project").
			Preload("User.Team").
			Preload("Replies.User")
	}
}

func PMOCommentWithVersions() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("CommentVersions")
	}
}

func PMOCommentByProjectID(projectID string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCommentByChannel(channel string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if channel != "" {
			c.Where("channel = ?", channel)
		}
	}
}

func PMOCommentByParentID(parentID string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if parentID != "" {
			c.Where("parent_comment_id = ?", parentID)
		} else {
			c.Where("parent_comment_id IS NULL")
		}
	}
}
