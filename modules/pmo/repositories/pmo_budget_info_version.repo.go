package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Budget Info Version Repository
var PMOBudgetInfoVersion = repository.Make[models.PMOBudgetInfoVersion]()

func PMOBudgetInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoVersionByBudgetInfoID(budgetInfoID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if budgetInfoID != "" {
			c.Where("budget_info_id = ?", budgetInfoID)
		}
	}
}

func PMOBudgetInfoVersionByProjectID(projectID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOBudgetInfoVersionWithRelations() repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		c.Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}
