package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Document Group Repository
var PMODocumentGroup = repository.Make[models.PMODocumentGroup]()

func PMODocumentGroupOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, group_name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentGroupWithProject() repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		c.Preload("Project")
	}
}

func PMODocumentGroupWithDocumentItems() repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		c.Preload("DocumentItems")
	}
}

func PMODocumentGroupByProjectID(projectID string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMODocumentGroupByTabKey(tabKey string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMODocumentGroupWithSearch(q string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("group_name ILIKE ?", searchTerm)
	}
}
