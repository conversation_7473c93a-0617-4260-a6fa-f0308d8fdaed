package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Bidbond Info Repository
var PMOBidbondInfo = repository.Make[models.PMOBidbondInfo]()

func PMOBidbondInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBidbondInfo] {
	return func(c repository.IRepository[models.PMOBidbondInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("start_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBidbondInfoByProjectID(projectID string) repository.Option[models.PMOBidbondInfo] {
	return func(c repository.IRepository[models.PMOBidbondInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOBidbondInfoWithRelations() repository.Option[models.PMOBidbondInfo] {
	return func(c repository.IRepository[models.PMOBidbondInfo]) {
		c.Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}
