package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Remark Version Repository
var PMORemarkVersion = repository.Make[models.PMORemarkVersion]()

func PMORemarkVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMORemarkVersionWithRelations() repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		c.Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}

func PMORemarkVersionByRemarkID(remarkID string) repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		if remarkID != "" {
			c.Where("remark_id = ?", remarkID)
		}
	}
}

func PMORemarkVersionByProjectID(projectID string) repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMORemarkVersionByTabKey(tabKey string) repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMORemarkVersionByProjectIDAndTabKey(projectID string, tabKey models.PMOTabKey) repository.Option[models.PMORemarkVersion] {
	return func(c repository.IRepository[models.PMORemarkVersion]) {
		if projectID != "" && tabKey != "" {
			c.Where("project_id = ? AND tab_key = ?", projectID, tabKey)
		}
	}
}
