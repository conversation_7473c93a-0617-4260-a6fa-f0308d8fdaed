package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Bidbond Info Version Repository
var PMOBidbondInfoVersion = repository.Make[models.PMOBidbondInfoVersion]()

func PMOBidbondInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBidbondInfoVersion] {
	return func(c repository.IRepository[models.PMOBidbondInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBidbondInfoVersionByBidbondInfoID(bidbondInfoID string) repository.Option[models.PMOBidbondInfoVersion] {
	return func(c repository.IRepository[models.PMOBidbondInfoVersion]) {
		if bidbondInfoID != "" {
			c.Where("bidbond_info_id = ?", bidbondInfoID)
		}
	}
}
