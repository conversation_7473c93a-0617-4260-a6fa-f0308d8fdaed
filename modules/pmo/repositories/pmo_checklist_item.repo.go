package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOChecklistItem = repository.Make[models.PMOChecklistItem]()

func PMOChecklistItemOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOChecklistItemWithRelations() repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		c.Preload("CreatedBy").Preload("UpdatedBy")
	}
}

func PMOChecklistItemByProjectID(projectID string) repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		c.Where("project_id = ?", projectID)
	}
}

func PMOChecklistItemWithTabKeyFilter(tabKey *string) repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		if tabKey != nil && *tabKey != "" {
			c.Where("tab_key = ?", *tabKey)
		}
	}
}

func PMOChecklistItemWithSearch(q string) repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		if q != "" {
			c.Where("detail ILIKE ?", "%"+q+"%")
		}
	}
}

func PMOChecklistItemWithIsCheckedFilter(isChecked *bool) repository.Option[models.PMOChecklistItem] {
	return func(c repository.IRepository[models.PMOChecklistItem]) {
		if isChecked != nil {
			c.Where("is_checked = ?", *isChecked)
		}
	}
}
