package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOTemplateChecklistItem = repository.Make[models.PMOTemplateChecklistItem]()

func PMOTemplateChecklistItemOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOTemplateChecklistItem] {
	return func(c repository.IRepository[models.PMOTemplateChecklistItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOTemplateChecklistItemWithSearch(q string) repository.Option[models.PMOTemplateChecklistItem] {
	return func(c repository.IRepository[models.PMOTemplateChecklistItem]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("detail ILIKE ?", searchTerm)
	}
}

func PMOTemplateChecklistItemWithTabKeyFilter(tabKey *string) repository.Option[models.PMOTemplateChecklistItem] {
	return func(c repository.IRepository[models.PMOTemplateChecklistItem]) {
		if tabKey != nil && *tabKey != "" {
			c.Where("tab_key = ?", *tabKey)
		}
	}
}
