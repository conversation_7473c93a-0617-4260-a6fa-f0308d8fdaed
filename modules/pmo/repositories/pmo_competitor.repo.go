package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Competitor Repository
var PMOCompetitor = repository.Make[models.PMOCompetitor]()

func PMOCompetitorOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCompetitor] {
	return func(c repository.IRepository[models.PMOCompetitor]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCompetitorWithRelations() repository.Option[models.PMOCompetitor] {
	return func(c repository.IRepository[models.PMOCompetitor]) {
		c.Preload("CreatedBy").Preload("UpdatedBy")
	}
}

func PMOCompetitorByProjectID(projectID string) repository.Option[models.PMOCompetitor] {
	return func(c repository.IRepository[models.PMOCompetitor]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}
