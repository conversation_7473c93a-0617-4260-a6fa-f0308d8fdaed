package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectPartnerService interface {
	Create(input *dto.PMOPartnerCreatePayload) (*models.PMOPartner, core.IError)
	Update(id string, input *dto.PMOPartnerUpdatePayload) (*models.PMOPartner, core.IError)
	Find(id string) (*models.PMOPartner, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOPartnerPaginationOptions) (*repository.Pagination[models.PMOPartner], core.IError)
	Delete(id string) core.IError
}

type pmoProjectPartnerService struct {
	ctx core.IContext
}

// PMO Partner methods implementation
func (s pmoProjectPartnerService) Create(input *dto.PMOPartnerCreatePayload) (*models.PMOPartner, core.IError) {
	partner := &models.PMOPartner{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		Company:     input.Company,
		Detail:      input.Detail,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOPartner(s.ctx).Create(partner)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(partner.ID)
}

func (s pmoProjectPartnerService) Update(id string, input *dto.PMOPartnerUpdatePayload) (*models.PMOPartner, core.IError) {
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	partner := &models.PMOPartner{
		Company:     input.Company,
		Detail:      input.Detail,
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOPartner(s.ctx).Where("id = ?", id).Updates(partner)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(id)
}

func (s pmoProjectPartnerService) Find(id string) (*models.PMOPartner, core.IError) {
	return repositories.PMOPartner(s.ctx,
		repositories.PMOPartnerWithRelations(),
	).FindOne("id = ?", id)
}

func (s pmoProjectPartnerService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOPartnerPaginationOptions) (*repository.Pagination[models.PMOPartner], core.IError) {
	return repositories.PMOPartner(s.ctx,
		repositories.PMOPartnerOrderBy(pageOptions),
		repositories.PMOPartnerWithRelations(),
		repositories.PMOPartnerByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s pmoProjectPartnerService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOPartner(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectPartnerService(ctx core.IContext) IPMOProjectPartnerService {
	return &pmoProjectPartnerService{ctx: ctx}
}
