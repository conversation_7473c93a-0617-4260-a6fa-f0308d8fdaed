package services

import (
	"gitlab.finema.co/finema/finework/finework-api/helpers"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectContactService interface {
	Create(input *dto.PMOContactCreatePayload) (*models.PMOContact, core.IError)
	Update(id string, input *dto.PMOContactUpdatePayload) (*models.PMOContact, core.IError)
	Find(id string) (*models.PMOContact, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOContactPaginationOptions) (*repository.Pagination[models.PMOContact], core.IError)
	Delete(id string) core.IError
}

type pmoProjectContactService struct {
	ctx core.IContext
}

// PMO Contact methods implementation
func (s pmoProjectContactService) Create(input *dto.PMOContactCreatePayload) (*models.PMOContact, core.IError) {
	contact := &models.PMOContact{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		Fullname:    input.Fullname,
		Phone:       input.Phone,
		Email:       input.Email,
		Detail:      input.Detail,
		Company:     input.Company,
		Position:    input.Position,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOContact(s.ctx).Create(contact)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(contact.ID)
}

func (s pmoProjectContactService) Update(id string, input *dto.PMOContactUpdatePayload) (*models.PMOContact, core.IError) {
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	ierr = repositories.PMOContact(s.ctx).Where("id = ?", id).Updates(helpers.ToMapUpdate(input))
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(id)
}

func (s pmoProjectContactService) Find(id string) (*models.PMOContact, core.IError) {
	return repositories.PMOContact(s.ctx,
		repositories.PMOContactWithRelations(),
	).FindOne("id = ?", id)
}

func (s pmoProjectContactService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOContactPaginationOptions) (*repository.Pagination[models.PMOContact], core.IError) {
	return repositories.PMOContact(s.ctx,
		repositories.PMOContactOrderBy(pageOptions),
		repositories.PMOContactWithRelations(),
		repositories.PMOContactByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s pmoProjectContactService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOContact(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectContactService(ctx core.IContext) IPMOProjectContactService {
	return &pmoProjectContactService{ctx: ctx}
}
