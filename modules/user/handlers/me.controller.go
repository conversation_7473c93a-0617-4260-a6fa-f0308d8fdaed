package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/modules/user/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MeController struct {
}

func (m MeController) GetProfile(c core.IHTTPContext) error {
	return c.JSON(http.StatusOK, c.GetData("user"))
}

func (m MeController) UpdateProfile(c core.IHTTPContext) error {
	userID := c.GetUser().ID

	input := &requests.MeUpdate{}
	if err := c.BindWith<PERSON>alidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	meSvc := services.NewMeService(c)
	payload := &services.MeUpdatePayload{}
	_ = utils.Copy(payload, input)

	user, err := meSvc.UpdateProfile(userID, payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON><PERSON><PERSON>())
	}

	return c.JSON(http.StatusOK, user)
}

func (m MeController) GetDevices(c core.IHTTPContext) error {
	userID := c.GetUser().ID

	meSvc := services.NewMeService(c)
	devices, err := meSvc.GetDevices(userID, c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, devices)
}

func (m MeController) DeleteDevice(c core.IHTTPContext) error {
	userID := c.GetUser().ID
	tokenID := c.Param("id")

	meSvc := services.NewMeService(c)
	err := meSvc.DeleteDevice(userID, tokenID)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
