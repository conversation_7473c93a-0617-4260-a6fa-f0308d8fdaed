package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserAccessLevelUpdate struct {
	core.BaseValidator
	Clockin   *string `json:"clockin"`
	Timesheet *string `json:"timesheet"`
	Pmo       *string `json:"pmo"`
	Setting   *string `json:"setting"`
}

func (r *UserAccessLevelUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Clockin, strings.Join([]string{
		string(models.UserPermissionLevelUser),
		string(models.UserPermissionLevelAdmin),
	}, "|"), "clockin"))

	r.Must(r.IsStrIn(r.Timesheet, strings.Join([]string{
		string(models.UserPermissionLevelUser),
		string(models.UserPermissionLevelAdmin),
	}, "|"), "timesheet"))

	r.Must(r.Is<PERSON>trIn(r.Pmo, strings.Join([]string{
		string(models.UserPermissionLevelNone),
		string(models.UserPermissionLevelUser),
		string(models.UserPermissionLevelAdmin),
		string(models.UserPermissionLevelSuper),
	}, "|"), "pmo"))

	r.Must(r.IsStrIn(r.Setting, strings.Join([]string{
		string(models.UserPermissionLevelNone),
		string(models.UserPermissionLevelSuper),
	}, "|"), "setting"))

	return r.Error()
}
