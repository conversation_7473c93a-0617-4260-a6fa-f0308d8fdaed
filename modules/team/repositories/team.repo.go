package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Team = repository.Make[models.Team]()

func TeamOrderBy(pageOptions *core.PageOptions) repository.Option[models.Team] {
	return func(c repository.IRepository[models.Team]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func TeamWithSearch(q string) repository.Option[models.Team] {
	return func(c repository.IRepository[models.Team]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}

func TeamWithTeamCode(code *string) repository.Option[models.Team] {
	return func(c repository.IRepository[models.Team]) {
		if code == nil {
			return
		}
		c.Where("code = ?", code)
	}
}
