package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MinistryCreate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>trRequired(r.NameTh, "name_th"))
	r.Must(r.Is<PERSON>trUnique(ctx, r.NameTh, models.Ministry{}.TableName(), "name_th", "", "name_th"))
	
	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Ministry{}.TableName(), "name_en", "", "name_en"))
	}

	return r.<PERSON>rror()
}
