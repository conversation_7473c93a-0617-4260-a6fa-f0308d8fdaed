model pmo_template_documents {
  id             String    @id @default(uuid()) @db.Uuid
  tab_key        PMOTabKey
  name           String
  sharepoint_url String
  created_at     DateTime  @default(now())
  created_by_id  String?   @db.Uuid
  updated_at     DateTime  @updatedAt
  updated_by_id  String?   @db.Uuid
  deleted_at     DateTime?
  deleted_by_id  String?   @db.Uuid

  @@index([tab_key])
}

model pmo_template_checklist_items {
  id            String    @id @default(uuid()) @db.Uuid
  tab_key       PMOTabKey
  detail        String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  @@index([tab_key])
}
