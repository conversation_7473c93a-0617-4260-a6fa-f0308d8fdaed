# PMO Project Collaborator API Documentation

This document describes the CRUD operations available for PMO Project Collaborators.

## Overview

PMO Project Collaborators manage user permissions for different tabs within PMO projects. Each collaborator can have different permission levels for various project tabs.

## Permission Levels

The following permission levels are supported:
- `READONLY` - User can view the tab content
- `MODIFY` - User can view and modify the tab content  
- `NONE` - User has no access to the tab

## Tab Types

The following tabs are supported:
- `CONFIDENTIAL` - Confidential information tab
- `SALES` - Sales information tab
- `PRESALES` - Presales information tab
- `BIDDING` - Bidding information tab
- `PMO` - PMO information tab

## API Endpoints

### 1. Get Collaborators (Pagination)

**GET** `/pmo/projects/{project_id}/collaborators`

Retrieves a paginated list of collaborators for a specific project.

**Query Parameters:**
- `tab_key` (optional): Filter by specific tab (CONFIDENTIAL, SALES, PRESALES, BIDDING, PMO)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `order_by` (optional): Sort order

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "project_id": "uuid",
      "user_id": "uuid",
      "confidential_permission": "READONLY",
      "confidential_main": false,
      "sales_permission": "MODIFY",
      "sales_main": true,
      "presales_permission": "NONE",
      "presales_main": false,
      "bidding_permission": "READONLY",
      "bidding_main": false,
      "pmo_permission": "MODIFY",
      "pmo_main": false,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "user": {
        "id": "uuid",
        "email": "<EMAIL>",
        "full_name": "John Doe"
      },
      "project": {
        "id": "uuid",
        "name": "Project Name"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "total_pages": 5
  }
}
```

### 2. Get Specific Collaborator

**GET** `/pmo/projects/{project_id}/collaborators/{collaborator_id}`

Retrieves details of a specific collaborator.

**Response:**
```json
{
  "id": "uuid",
  "project_id": "uuid",
  "user_id": "uuid",
  "confidential_permission": "READONLY",
  "confidential_main": false,
  "sales_permission": "MODIFY",
  "sales_main": true,
  "presales_permission": "NONE",
  "presales_main": false,
  "bidding_permission": "READONLY",
  "bidding_main": false,
  "pmo_permission": "MODIFY",
  "pmo_main": false,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe"
  },
  "project": {
    "id": "uuid",
    "name": "Project Name"
  }
}
```

### 3. Create Collaborator

**POST** `/pmo/projects/{project_id}/collaborators`

Creates a new collaborator for the project.

**Request Body:**
```json
{
  "user_id": "uuid",
  "confidential_permission": "READONLY",
  "confidential_main": false,
  "sales_permission": "MODIFY",
  "sales_main": true,
  "presales_permission": "NONE",
  "presales_main": false,
  "bidding_permission": "READONLY",
  "bidding_main": false,
  "pmo_permission": "MODIFY",
  "pmo_main": false
}
```

**Response:** Returns the created collaborator object (same structure as GET response).

### 4. Update Collaborator

**PUT** `/pmo/projects/{project_id}/collaborators/{collaborator_id}`

Updates an existing collaborator's permissions.

**Request Body:**
```json
{
  "confidential_permission": "MODIFY",
  "confidential_main": true,
  "sales_permission": "READONLY",
  "sales_main": false,
  "presales_permission": "MODIFY",
  "presales_main": true,
  "bidding_permission": "NONE",
  "bidding_main": false,
  "pmo_permission": "READONLY",
  "pmo_main": false
}
```

**Response:** Returns the updated collaborator object.

### 5. Delete Collaborator

**DELETE** `/pmo/projects/{project_id}/collaborators/{collaborator_id}`

Soft deletes a collaborator from the project.

**Response:** 204 No Content

## Error Responses

### 400 Bad Request
```json
{
  "code": "INVALID_PARAMS",
  "message": "Invalid params request",
  "errors": [
    {
      "name": "user_id",
      "code": "REQUIRED",
      "message": "user_id is required"
    }
  ]
}
```

### 409 Conflict
```json
{
  "code": "PMO_COLLABORATOR_ALREADY_EXISTS",
  "message": "Collaborator already exists for this project"
}
```

### 404 Not Found
```json
{
  "code": "NOT_FOUND",
  "message": "Collaborator not found"
}
```

## Notes

- All endpoints require authentication via Bearer token
- The `project_id` in the URL path is automatically set for create operations
- Each user can only be a collaborator once per project
- The `*_main` fields indicate if the user is the main responsible person for that tab
- Soft delete is used, so deleted collaborators are not permanently removed from the database
